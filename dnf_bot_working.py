#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF搬砖机器人 - 工作版本
基于已验证有效的驱动级输入方法
"""

import ctypes
import time
import os
import configparser
import logging
from datetime import datetime
from ctypes import wintypes, Structure, Union, POINTER, byref

# 扫描码映射
SCAN_CODES = {
    'a': 0x1E, 'b': 0x30, 'c': 0x2E, 'd': 0x20, 'e': 0x12, 'f': 0x21,
    'g': 0x22, 'h': 0x23, 'i': 0x17, 'j': 0x24, 'k': 0x25, 'l': 0x26,
    'm': 0x32, 'n': 0x31, 'o': 0x18, 'p': 0x19, 'q': 0x10, 'r': 0x13,
    's': 0x1F, 't': 0x14, 'u': 0x16, 'v': 0x2F, 'w': 0x11, 'x': 0x2D,
    'y': 0x15, 'z': 0x2C,
    '0': 0x0B, '1': 0x02, '2': 0x03, '3': 0x04, '4': 0x05,
    '5': 0x06, '6': 0x07, '7': 0x08, '8': 0x09, '9': 0x0A,
    'space': 0x39, 'enter': 0x1C, 'esc': 0x01, 'tab': 0x0F,
    'shift': 0x2A, 'ctrl': 0x1D, 'alt': 0x38,
    'left': 0x4B, 'up': 0x48, 'right': 0x4D, 'down': 0x50,
}

class DNFWorkingBot:
    """DNF工作版搬砖机器人"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.game_hwnd = None
        self.game_pid = None
        
        # 配置参数
        self.skill_key = 'e'
        self.work_hours = 8
        self.use_stall_decompose = True
        
        # 运行状态
        self.running = False
        self.start_time = None
        
        # 初始化
        self.setup_logging()
        self.load_config()
        self.enable_debug_privilege()
    
    def setup_logging(self):
        """设置日志"""
        os.makedirs("logs", exist_ok=True)
        log_file = f"logs/dnf_bot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """加载配置"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            self.work_hours = config.getint('GENERAL', 'work_hours', fallback=8)
            self.use_stall_decompose = config.getboolean('FEATURES', 'use_stall_decompose', fallback=True)
            
            self.logger.info(f"配置加载完成 - 技能键: {self.skill_key}, 工作时长: {self.work_hours}小时")
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                self.logger.info("调试权限已启用")
        except Exception as e:
            self.logger.warning(f"调试权限启用失败: {e}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        pid = wintypes.DWORD()
                        self.user32.GetWindowThreadProcessId(hwnd, byref(pid))
                        self.game_hwnd = hwnd
                        self.game_pid = pid.value
                        self.logger.info(f"找到DNF窗口: {window_title} (PID: {self.game_pid})")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        return self.game_hwnd is not None
    
    def send_key(self, key, duration=0.05):
        """发送按键（使用已验证有效的方法）"""
        scan_code = SCAN_CODES.get(key.lower())
        if not scan_code:
            self.logger.error(f"未知按键: {key}")
            return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.02)
        
        try:
            # 使用扫描码发送按键
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(duration)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            
            self.logger.debug(f"发送按键: {key}")
            return True
        except Exception as e:
            self.logger.error(f"发送按键失败 {key}: {e}")
            return False
    
    def send_combination(self, keys):
        """发送组合键"""
        scan_codes = []
        for key in keys:
            scan_code = SCAN_CODES.get(key.lower())
            if scan_code:
                scan_codes.append(scan_code)
            else:
                self.logger.error(f"未知按键: {key}")
                return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.02)
        
        try:
            # 按下所有键
            for scan_code in scan_codes:
                self.user32.keybd_event(0, scan_code, 0x0008, 0)
                time.sleep(0.01)
            
            time.sleep(0.05)
            
            # 释放所有键（逆序）
            for scan_code in reversed(scan_codes):
                self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
                time.sleep(0.01)
            
            self.logger.debug(f"发送组合键: {'+'.join(keys)}")
            return True
        except Exception as e:
            self.logger.error(f"发送组合键失败: {e}")
            return False
    
    def enter_grandi(self):
        """进入格兰迪地下城"""
        self.logger.info("开始进入格兰迪")
        
        # 1. 向右进入地图选择
        self.logger.info("1. 进入地图选择")
        self.send_key('right')
        time.sleep(2)
        
        # 2. 确认进入地图
        self.logger.info("2. 确认进入地图")
        self.send_key('space')
        time.sleep(4)  # 等待加载
        
        self.logger.info("成功进入格兰迪")
    
    def clear_dungeon(self):
        """清理地下城"""
        self.logger.info("开始清理地下城")
        
        for room in range(8):
            self.logger.info(f"清理房间 {room + 1}/8")
            
            # 移动到房间右侧
            self.logger.debug("向右移动")
            for _ in range(3):  # 多次移动确保到位
                self.send_key('right', duration=0.3)
                time.sleep(0.2)
            
            # 释放技能攻击
            self.logger.debug(f"释放技能 {self.skill_key}")
            for _ in range(6):  # 多次攻击确保清空
                self.send_key(self.skill_key)
                time.sleep(0.15)
            
            # 等待怪物死亡和掉落
            time.sleep(1.5)
            
            # 顺图到下一房间
            if room < 7:
                self.logger.debug("顺图到下一房间")
                self.send_combination(['alt', 'right'])
                time.sleep(2)
        
        self.logger.info("地下城清理完成")
    
    def exit_dungeon(self):
        """退出地下城"""
        self.logger.info("退出地下城")
        
        # 多次按ESC确保退出
        for i in range(3):
            self.logger.debug(f"按ESC第{i+1}次")
            self.send_key('esc')
            time.sleep(1.5)
        
        # 按数字键4（可能是某个功能）
        self.logger.debug("按数字键4")
        self.send_key('4')
        time.sleep(2)
    
    def handle_inventory(self):
        """处理背包"""
        if self.use_stall_decompose:
            self.logger.info("处理背包 - 摆摊分解")
            # 按0键摆摊分解
            self.send_key('0')
            time.sleep(3)
            
            # 按Alt+D分解
            self.send_combination(['alt', 'd'])
            time.sleep(2)
            
            # 按Alt+W修理装备
            self.send_combination(['alt', 'w'])
            time.sleep(1)
    
    def farming_cycle(self):
        """完整的搬砖循环"""
        cycle_start = time.time()
        self.logger.info("=" * 50)
        self.logger.info("开始搬砖循环")
        
        try:
            # 1. 进入格兰迪
            self.enter_grandi()
            
            # 2. 清理地下城
            self.clear_dungeon()
            
            # 3. 退出地下城
            self.exit_dungeon()
            
            # 4. 处理背包
            self.handle_inventory()
            
            cycle_time = time.time() - cycle_start
            self.logger.info(f"搬砖循环完成，耗时: {cycle_time:.1f}秒")
            
        except Exception as e:
            self.logger.error(f"搬砖循环异常: {e}")
    
    def is_work_time_exceeded(self):
        """检查是否超过工作时长"""
        if self.start_time is None:
            return False
        
        elapsed = time.time() - self.start_time
        return elapsed >= (self.work_hours * 3600)
    
    def start_bot(self):
        """启动机器人"""
        if not self.find_dnf_window():
            self.logger.error("未找到DNF窗口，请确保游戏已启动")
            return
        
        self.logger.info("=" * 50)
        self.logger.info("DNF搬砖机器人启动")
        self.logger.info(f"工作时长: {self.work_hours}小时")
        self.logger.info(f"技能键: {self.skill_key}")
        self.logger.info(f"摆摊分解: {'开启' if self.use_stall_decompose else '关闭'}")
        self.logger.info("按Ctrl+C停止机器人")
        self.logger.info("=" * 50)
        
        self.running = True
        self.start_time = time.time()
        cycle_count = 0
        
        try:
            while self.running:
                # 检查工作时长
                if self.is_work_time_exceeded():
                    self.logger.info("已达到设定工作时长，机器人停止")
                    break
                
                cycle_count += 1
                self.logger.info(f"\n第 {cycle_count} 轮搬砖开始")
                
                # 执行搬砖循环
                self.farming_cycle()
                
                # 等待下一轮
                self.logger.info("等待下一轮...")
                time.sleep(3)
                
        except KeyboardInterrupt:
            self.logger.info("用户中断机器人")
        except Exception as e:
            self.logger.error(f"机器人运行异常: {e}")
        finally:
            self.running = False
            total_time = time.time() - self.start_time if self.start_time else 0
            self.logger.info(f"机器人已停止，总运行时间: {total_time/3600:.1f}小时")

def main():
    """主函数"""
    print("DNF搬砖机器人 - 工作版本")
    print("=" * 40)
    print("基于已验证有效的驱动级输入方法")
    print()
    
    # 检查管理员权限
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            print("⚠ 警告: 建议以管理员权限运行以获得最佳效果")
    except:
        pass
    
    bot = DNFWorkingBot()
    
    while True:
        print("\n请选择操作:")
        print("1. 启动自动搬砖")
        print("2. 测试按键功能")
        print("3. 单次搬砖测试")
        print("4. 查看配置")
        print("0. 退出")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            print("\n请确保:")
            print("- DNF角色在城镇中")
            print("- 没有打开对话框或菜单")
            print("- 角色面向地图传送门")
            print("- 有足够的疲劳值")
            input("\n确认无误后按回车开始...")
            bot.start_bot()
        elif choice == '2':
            if not bot.find_dnf_window():
                print("未找到DNF窗口")
                continue
            key = input("请输入要测试的按键: ").strip()
            if key:
                if bot.send_key(key):
                    print("✓ 按键发送成功")
                else:
                    print("✗ 按键发送失败")
        elif choice == '3':
            if not bot.find_dnf_window():
                print("未找到DNF窗口")
                continue
            print("执行单次搬砖测试...")
            bot.farming_cycle()
        elif choice == '4':
            print(f"\n当前配置:")
            print(f"技能键: {bot.skill_key}")
            print(f"工作时长: {bot.work_hours}小时")
            print(f"摆摊分解: {'开启' if bot.use_stall_decompose else '关闭'}")
        else:
            print("无效选择")
    
    print("程序结束")

if __name__ == "__main__":
    main()
