@echo off
chcp 65001 >nul
title DNF格兰迪搬砖脚本

echo ========================================
echo DNF格兰迪搬砖自动化脚本
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 检查依赖包...
pip show opencv-python >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 创建必要目录...
if not exist "logs" mkdir logs
if not exist "images" mkdir images

echo.
echo 请选择运行模式:
echo 1. 启动搬砖脚本
echo 2. 运行模块测试
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo.
    echo 启动搬砖脚本...
    echo 注意: 请确保DNF游戏已启动并位于城镇
    echo 按Ctrl+C可以安全退出脚本
    echo.
    pause
    python main.py
) else if "%choice%"=="2" (
    echo.
    echo 运行模块测试...
    python test_modules.py
) else if "%choice%"=="3" (
    exit /b 0
) else (
    echo 无效选择
    pause
    goto :eof
)

echo.
echo 程序已结束
pause
