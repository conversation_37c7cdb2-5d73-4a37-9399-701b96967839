#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF格兰迪搬砖脚本
基于文档要求重新编写的简洁版本
"""

import ctypes
import time
import os
import cv2
import numpy as np
from PIL import ImageGrab
import configparser
import logging
from datetime import datetime
from ctypes import wintypes, byref

class DNFGrandiBot:
    """DNF格兰迪搬砖机器人"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.game_hwnd = None
        self.window_rect = None
        
        # 配置参数
        self.skill_key = 'e'  # 技能释放键
        self.work_hours = 8   # 搬砖时长（小时）
        self.use_stall_decompose = True  # 是否摆摊分解
        self.enable_abyss = False  # 是否刷深渊
        
        # 运行状态
        self.running = False
        self.start_time = None
        
        # 初始化
        self.setup_logging()
        self.load_config()
        self.enable_debug_privilege()
    
    def setup_logging(self):
        """设置日志"""
        os.makedirs("logs", exist_ok=True)
        log_file = f"logs/dnf_grandi_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            self.work_hours = config.getint('GENERAL', 'work_hours', fallback=8)
            self.use_stall_decompose = config.getboolean('FEATURES', 'use_stall_decompose', fallback=True)
            self.enable_abyss = config.getboolean('FEATURES', 'enable_abyss', fallback=False)
            
            self.logger.info(f"配置加载完成 - 技能键: {self.skill_key}, 工作时长: {self.work_hours}小时")
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                self.logger.info("调试权限已启用")
        except Exception as e:
            self.logger.warning(f"调试权限启用失败: {e}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        self.logger.info(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def send_key(self, key):
        """发送按键 - 使用验证有效的方法"""
        # 扫描码映射
        scan_codes = {
            'e': 0x12, 'space': 0x39, 'esc': 0x01, '4': 0x05, '0': 0x0B,
            'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0,
            'alt': 0x38, 'f11': 0x57, 'i': 0x17, 'd': 0x20, 'w': 0x11, 'q': 0x10
        }
        
        scan_code = scan_codes.get(key.lower())
        if not scan_code:
            self.logger.error(f"未知按键: {key}")
            return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            return True
        except Exception as e:
            self.logger.error(f"发送按键失败: {e}")
            return False
    
    def send_combination(self, keys):
        """发送组合键"""
        scan_codes = {
            'alt': 0x38, 'q': 0x10, 'w': 0x11, 'd': 0x20,
            'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0
        }
        
        codes = []
        for key in keys:
            code = scan_codes.get(key.lower())
            if code:
                codes.append(code)
            else:
                self.logger.error(f"未知组合键: {key}")
                return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 按下所有键
            for code in codes:
                self.user32.keybd_event(0, code, 0x0008, 0)
                time.sleep(0.01)
            
            time.sleep(0.1)
            
            # 释放所有键（逆序）
            for code in reversed(codes):
                self.user32.keybd_event(0, code, 0x0008 | 0x0002, 0)
                time.sleep(0.01)
            
            return True
        except Exception as e:
            self.logger.error(f"发送组合键失败: {e}")
            return False
    
    def capture_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None

        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)

            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            screen = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            return screen
        except Exception as e:
            self.logger.error(f"截取画面失败: {e}")
            return None

    def capture_minimap(self):
        """截取右上角小地图区域"""
        screen = self.capture_screen()
        if screen is None:
            return None

        try:
            # 提取右上角小地图区域（根据实际情况调整）
            h, w = screen.shape[:2]
            minimap = screen[50:200, w-200:w-50]  # 右上角区域

            return minimap
        except Exception as e:
            self.logger.error(f"截取小地图失败: {e}")
            return None

    def find_door_by_light(self):
        """通过灯光颜色寻找门（蓝灯=普通房，黄灯=BOSS房）"""
        screen = self.capture_screen()
        if screen is None:
            return None

        try:
            h, w = screen.shape[:2]

            # 转换为HSV色彩空间，更容易检测颜色
            hsv = cv2.cvtColor(screen, cv2.COLOR_BGR2HSV)

            # 定义蓝灯和黄灯的HSV范围
            # 蓝灯范围（普通房间）
            blue_lower = np.array([100, 50, 50])
            blue_upper = np.array([130, 255, 255])

            # 黄灯范围（BOSS房间）
            yellow_lower = np.array([20, 50, 50])
            yellow_upper = np.array([30, 255, 255])

            # 检测蓝灯
            blue_mask = cv2.inRange(hsv, blue_lower, blue_upper)
            yellow_mask = cv2.inRange(hsv, yellow_lower, yellow_upper)

            # 合并两个掩码
            door_mask = cv2.bitwise_or(blue_mask, yellow_mask)

            # 寻找轮廓
            contours, _ = cv2.findContours(door_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 收集所有可能的门灯
            doors = []

            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 100:  # 门灯的最小面积
                    x, y, w_door, h_door = cv2.boundingRect(contour)

                    # 检查是否在画面右侧（门应该在右边）
                    if x > w * 0.5:  # 在画面右半部分
                        door_x = x + w_door // 2
                        door_y = y + h_door // 2

                        # 判断门的类型
                        center_color = hsv[door_y, door_x]
                        if blue_lower[0] <= center_color[0] <= blue_upper[0]:
                            door_type = "蓝灯(普通房)"
                        else:
                            door_type = "黄灯(BOSS房)"

                        doors.append((door_x, door_y, door_type, area))
                        self.logger.debug(f"找到门灯: {door_type}, 位置({door_x}, {door_y}), 面积={area}")

            # 选择最大的门灯作为目标
            if doors:
                # 按面积排序，选择最大的
                best_door = max(doors, key=lambda x: x[3])
                self.logger.info(f"选择最大门灯: {best_door[2]}, 位置({best_door[0]}, {best_door[1]})")
                return best_door[:3]  # 返回 (x, y, type)
            else:
                self.logger.debug("当前画面未找到门灯")
                return None

        except Exception as e:
            self.logger.error(f"门灯检测失败: {e}")
            return None

    def find_and_move_to_door(self):
        """寻找门并移动到门口"""
        self.logger.info("开始寻找右边的门")

        # 最多尝试向右移动10次寻找门
        for attempt in range(10):
            self.logger.debug(f"寻找门尝试 {attempt + 1}/10")

            # 检测当前画面是否有门
            door_info = self.find_door_by_light()

            if door_info:
                door_x, door_y, door_type = door_info
                self.logger.info(f"找到门: {door_type}, 位置({door_x}, {door_y})")

                # 移动到门的位置
                screen = self.capture_screen()
                if screen is None:
                    return False

                h, w = screen.shape[:2]
                char_x = w // 2  # 假设角色在画面中央

                # 计算移动距离
                if door_x > char_x + 50:  # 门在右边且距离较远
                    move_steps = min(5, (door_x - char_x) // 80)
                    self.logger.info(f"向右移动 {move_steps} 步到达门口")

                    for _ in range(move_steps):
                        self.send_key('right')
                        time.sleep(0.2)
                elif door_x < char_x - 50:  # 门在左边
                    move_steps = min(3, (char_x - door_x) // 80)
                    self.logger.info(f"向左移动 {move_steps} 步到达门口")

                    for _ in range(move_steps):
                        self.send_key('left')
                        time.sleep(0.2)

                self.logger.info("已到达门口位置")
                return True
            else:
                # 当前画面没有门，继续向右寻找
                self.logger.debug("当前画面无门，继续向右寻找")
                for _ in range(3):  # 每次向右移动3步
                    self.send_key('right')
                    time.sleep(0.15)

        self.logger.warning("未找到门，已到达房间最右侧")
        return False
    
    def is_in_town(self):
        """检测是否在城镇（基于小地图）"""
        minimap = self.capture_minimap()
        if minimap is None:
            return False
        
        # 这里需要根据实际的城镇小地图特征来判断
        # 暂时返回True，实际使用时需要添加图像识别逻辑
        return True
    
    def enter_grandi(self):
        """进入格兰迪地下城"""
        self.logger.info("开始进入格兰迪")
        
        # 1. 检查是否在城镇
        if not self.is_in_town():
            self.logger.info("不在城镇，尝试传送回城")
            # 这里需要添加传送回城的逻辑
            return False
        
        # 2. 向右走进入地图选择
        self.logger.info("向右走进入地图选择")
        self.send_key('right')
        time.sleep(3)
        
        # 3. 选择格兰迪和地狱难度（需要手动预设）
        self.logger.info("等待选择格兰迪和地狱难度")
        time.sleep(1)
        
        # 4. 如果启用深渊模式
        if self.enable_abyss:
            self.logger.info("开启深渊模式")
            self.send_key('f11')
            time.sleep(0.5)
        
        # 5. 确认进入
        self.logger.info("确认进入格兰迪")
        self.send_key('space')
        time.sleep(5)  # 等待加载
        
        return True
    
    def clear_room(self):
        """清理当前房间"""
        self.logger.info("开始清理房间")

        # 先向右移动一些距离
        for _ in range(6):
            self.send_key('right')
            time.sleep(0.15)

        # 释放技能清理怪物
        for _ in range(12):
            self.send_key(self.skill_key)
            time.sleep(0.2)

            # 边攻击边移动
            if _ % 3 == 0:
                self.send_key('right')
                time.sleep(0.1)

        # 等待怪物死亡和掉落
        time.sleep(2)

        self.logger.info("房间清理完成")
    
    def move_to_next_room(self):
        """移动到下一房间"""
        self.logger.info("准备移动到下一房间")

        # 1. 寻找并移动到门口
        if self.find_and_move_to_door():
            # 2. 尝试进入门
            self.logger.info("尝试进入门")

            # 方法1: 直接向右走进门
            self.logger.debug("方法1: 直接向右走")
            for _ in range(4):
                self.send_key('right')
                time.sleep(0.2)

            # 方法2: 按空格
            self.logger.debug("方法2: 按空格")
            self.send_key('space')
            time.sleep(1)

            # 方法3: 尝试上下左右进入
            directions = ['up', 'down', 'left', 'right']
            for direction in directions:
                self.logger.debug(f"方法3: 尝试{direction}")
                self.send_key(direction)
                time.sleep(0.5)

            self.logger.info("已尝试进入下一房间")
            return True
        else:
            # 如果找不到门，使用传统顺图方法
            self.logger.warning("未找到门，使用传统顺图方法")

            # 尝试各种顺图组合键
            methods = [
                (['alt', 'right'], "Alt+Right"),
                (['alt', 'down'], "Alt+Down"),
                (['alt', 'up'], "Alt+Up"),
                (['alt', 'left'], "Alt+Left"),
            ]

            for method, name in methods:
                self.logger.info(f"尝试: {name}")
                self.send_combination(method)
                time.sleep(2)

                # 简单检测：如果画面发生变化，可能成功了
                # 这里可以添加更复杂的检测逻辑
                break  # 暂时只尝试第一种方法

        return True
    
    def clear_dungeon(self):
        """清理整个地下城"""
        self.logger.info("开始清理地下城")
        
        for room in range(8):  # 格兰迪8个房间
            self.logger.info(f"清理房间 {room + 1}/8")
            
            # 清理当前房间
            self.clear_room()
            
            # 移动到下一房间（除了最后一个）
            if room < 7:
                self.move_to_next_room()
        
        self.logger.info("地下城清理完成")
    
    def exit_dungeon(self):
        """退出地下城"""
        self.logger.info("退出地下城")
        
        # 等待翻牌界面
        time.sleep(3)
        
        # 多次按ESC直到返回城镇
        for _ in range(5):
            self.send_key('esc')
            time.sleep(2)
            
            # 检查是否回到城镇
            if self.is_in_town():
                self.logger.info("成功返回城镇")
                return True
        
        return True
    
    def handle_inventory(self):
        """处理背包"""
        self.logger.info("处理背包")
        
        if self.use_stall_decompose:
            # 摆摊分解
            self.logger.info("使用摆摊分解")
            self.send_key('0')  # 摆摊分解机
            time.sleep(3)
            
            # 分解装备
            self.send_combination(['alt', 'd'])
            time.sleep(2)
            
            # 修理装备
            self.send_combination(['alt', 'w'])
            time.sleep(1)
            
            # 按数字键4
            self.send_key('4')
            time.sleep(1)
        else:
            # 直接分解
            self.send_combination(['alt', 'd'])
            time.sleep(1)
    
    def farming_cycle(self):
        """完整的搬砖循环"""
        self.logger.info("=" * 50)
        self.logger.info("开始搬砖循环")
        
        try:
            # 1. 进入格兰迪
            if not self.enter_grandi():
                return False
            
            # 2. 清理地下城
            self.clear_dungeon()
            
            # 3. 退出地下城
            self.exit_dungeon()
            
            # 4. 处理背包
            self.handle_inventory()
            
            self.logger.info("搬砖循环完成")
            return True
            
        except Exception as e:
            self.logger.error(f"搬砖循环异常: {e}")
            return False
    
    def start_bot(self):
        """启动机器人"""
        if not self.find_dnf_window():
            self.logger.error("未找到DNF窗口")
            return
        
        self.logger.info("=" * 50)
        self.logger.info("DNF格兰迪搬砖机器人启动")
        self.logger.info(f"技能键: {self.skill_key}")
        self.logger.info(f"工作时长: {self.work_hours}小时")
        self.logger.info(f"摆摊分解: {'开启' if self.use_stall_decompose else '关闭'}")
        self.logger.info(f"深渊模式: {'开启' if self.enable_abyss else '关闭'}")
        self.logger.info("=" * 50)
        
        self.running = True
        self.start_time = time.time()
        cycle_count = 0
        
        try:
            while self.running:
                # 检查工作时长
                if time.time() - self.start_time >= (self.work_hours * 3600):
                    self.logger.info("已达到设定工作时长")
                    break
                
                cycle_count += 1
                self.logger.info(f"\n第 {cycle_count} 轮搬砖开始")
                
                # 执行搬砖循环
                self.farming_cycle()
                
                # 等待下一轮
                time.sleep(5)
                
        except KeyboardInterrupt:
            self.logger.info("用户中断")
        finally:
            self.running = False
            total_time = (time.time() - self.start_time) / 3600
            self.logger.info(f"机器人停止，总运行时间: {total_time:.1f}小时")

def create_config():
    """创建配置文件"""
    config = configparser.ConfigParser()
    
    config['GENERAL'] = {
        'skill_key': 'e',
        'work_hours': '8'
    }
    
    config['FEATURES'] = {
        'use_stall_decompose': 'true',
        'enable_abyss': 'false'
    }
    
    with open('config.ini', 'w', encoding='utf-8') as f:
        config.write(f)
    
    print("配置文件 config.ini 已创建")

def main():
    """主函数"""
    print("DNF格兰迪搬砖机器人")
    print("=" * 30)
    
    # 检查配置文件
    if not os.path.exists('config.ini'):
        print("未找到配置文件，正在创建...")
        create_config()
    
    bot = DNFGrandiBot()
    
    while True:
        print("\n请选择操作:")
        print("1. 启动搬砖机器人")
        print("2. 测试按键功能")
        print("3. 查看配置")
        print("0. 退出")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            print("\n请确保:")
            print("- 角色在城镇中")
            print("- 面向地图传送门")
            print("- 已手动选择格兰迪和地狱难度")
            input("确认后按回车开始...")
            bot.start_bot()
        elif choice == '2':
            if bot.find_dnf_window():
                print("测试按键功能...")
                print("1. 测试移动")
                bot.send_key('right')
                time.sleep(1)
                print("2. 测试技能")
                bot.send_key(bot.skill_key)
                time.sleep(1)
                print("3. 测试门灯识别")
                door_info = bot.find_door_by_light()
                if door_info:
                    door_x, door_y, door_type = door_info
                    print(f"找到门: {door_type}, 位置=({door_x}, {door_y})")
                    choice = input("是否测试寻找并移动到门? (y/n): ").strip().lower()
                    if choice == 'y':
                        bot.find_and_move_to_door()
                else:
                    print("当前画面未找到门灯")
                    print("可能需要向右移动寻找门")
            else:
                print("未找到DNF窗口")
        elif choice == '3':
            print(f"\n当前配置:")
            print(f"技能键: {bot.skill_key}")
            print(f"工作时长: {bot.work_hours}小时")
            print(f"摆摊分解: {'开启' if bot.use_stall_decompose else '关闭'}")
            print(f"深渊模式: {'开启' if bot.enable_abyss else '关闭'}")
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
