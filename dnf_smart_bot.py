#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF智能搬砖机器人
带有图像识别和状态判断的版本
"""

import ctypes
import time
import os
import cv2
import numpy as np
from PIL import ImageGrab
import configparser
import logging
from datetime import datetime
from ctypes import wintypes, byref

# 扫描码（使用验证有效的版本）
HARDWARE_SCAN_CODES = {
    'a': 0x1E, 'b': 0x30, 'c': 0x2E, 'd': 0x20, 'e': 0x12, 'f': 0x21,
    'g': 0x22, 'h': 0x23, 'i': 0x17, 'j': 0x24, 'k': 0x25, 'l': 0x26,
    'm': 0x32, 'n': 0x31, 'o': 0x18, 'p': 0x19, 'q': 0x10, 'r': 0x13,
    's': 0x1F, 't': 0x14, 'u': 0x16, 'v': 0x2F, 'w': 0x11, 'x': 0x2D,
    'y': 0x15, 'z': 0x2C,
    '0': 0x0B, '1': 0x02, '2': 0x03, '3': 0x04, '4': 0x05,
    '5': 0x06, '6': 0x07, '7': 0x08, '8': 0x09, '9': 0x0A,
    'space': 0x39, 'enter': 0x1C, 'esc': 0x01, 'tab': 0x0F,
    'shift': 0x2A, 'ctrl': 0x1D, 'alt': 0x38,
    'left': 0xCB, 'up': 0xC8, 'right': 0xCD, 'down': 0xD0,
    'f1': 0x3B, 'f2': 0x3C, 'f3': 0x3D, 'f4': 0x3E, 'f5': 0x3F,
    'f11': 0x57,
}

class DNFSmartBot:
    """DNF智能搬砖机器人"""
    
    def __init__(self):
        self.kernel32 = ctypes.windll.kernel32
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.game_pid = None
        
        # 配置
        self.skill_key = 'e'
        self.work_hours = 8
        
        # 窗口信息
        self.window_rect = None
        
        # 初始化
        self.setup_logging()
        self.load_config()
        self.enable_debug_privilege()
        
        # 创建截图目录
        os.makedirs("screenshots", exist_ok=True)
    
    def setup_logging(self):
        """设置日志"""
        os.makedirs("logs", exist_ok=True)
        log_file = f"logs/dnf_smart_bot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """加载配置"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            self.work_hours = config.getint('GENERAL', 'work_hours', fallback=8)
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                self.logger.info("调试权限已启用")
        except Exception as e:
            self.logger.warning(f"调试权限启用失败: {e}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        pid = wintypes.DWORD()
                        self.user32.GetWindowThreadProcessId(hwnd, byref(pid))
                        
                        self.game_hwnd = hwnd
                        self.game_pid = pid.value
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        self.logger.info(f"找到DNF窗口: {window_title}")
                        self.logger.info(f"窗口位置: {self.window_rect}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def capture_game_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            self.logger.error(f"截屏失败: {e}")
            return None
    
    def save_screenshot(self, name):
        """保存截图"""
        screen = self.capture_game_screen()
        if screen is not None:
            filename = f"screenshots/{name}_{datetime.now().strftime('%H%M%S')}.png"
            cv2.imwrite(filename, screen)
            self.logger.info(f"截图已保存: {filename}")
            return filename
        return None
    
    def send_key(self, key):
        """发送按键（使用验证有效的方法）"""
        scan_code = HARDWARE_SCAN_CODES.get(key.lower())
        if not scan_code:
            self.logger.error(f"未知按键: {key}")
            return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 使用扫描码发送
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            
            self.logger.debug(f"发送按键: {key}")
            return True
        except Exception as e:
            self.logger.error(f"发送按键失败 {key}: {e}")
            return False
    
    def detect_game_state(self):
        """检测游戏状态"""
        screen = self.capture_game_screen()
        if screen is None:
            return "unknown"
        
        # 这里需要根据实际游戏界面来判断
        # 暂时返回基础状态
        return "town"  # 城镇、map_selection、dungeon、flip_card
    
    def wait_for_state_change(self, timeout=10):
        """等待状态变化"""
        start_time = time.time()
        initial_screen = self.capture_game_screen()
        
        while time.time() - start_time < timeout:
            current_screen = self.capture_game_screen()
            if current_screen is not None and initial_screen is not None:
                # 计算图像差异
                diff = cv2.absdiff(initial_screen, current_screen)
                diff_sum = np.sum(diff)
                
                # 如果差异足够大，说明界面发生了变化
                if diff_sum > 1000000:  # 阈值需要调整
                    self.logger.info("检测到界面变化")
                    return True
            
            time.sleep(0.5)
        
        self.logger.warning("等待状态变化超时")
        return False
    
    def interactive_setup(self):
        """交互式设置"""
        print("\n=== DNF智能搬砖机器人设置 ===")
        print("由于需要图像识别，我们需要先进行一些设置")
        print()
        
        if not self.find_dnf_window():
            print("错误: 未找到DNF窗口")
            return False
        
        print("1. 请确保角色在城镇中")
        print("2. 面向地图传送门")
        print("3. 没有任何对话框或菜单打开")
        input("确认后按回车继续...")
        
        # 保存当前状态截图
        self.save_screenshot("town_state")
        print("已保存城镇状态截图")
        
        print("\n现在我们来测试进入地图选择:")
        print("我将按右键，请观察是否进入地图选择界面")
        input("准备好后按回车...")
        
        # 测试进入地图选择
        self.send_key('right')
        time.sleep(2)
        
        # 保存地图选择截图
        self.save_screenshot("map_selection")
        print("已保存地图选择界面截图")
        
        choice = input("是否成功进入地图选择界面? (y/n): ").strip().lower()
        if choice != 'y':
            print("请手动调整角色位置，确保能正确进入地图选择")
            return False
        
        print("\n请手动选择格兰迪和地狱难度")
        print("选择完成后不要按空格确认")
        input("选择完成后按回车...")
        
        # 保存选择完成的截图
        self.save_screenshot("grandi_selected")
        print("已保存格兰迪选择截图")
        
        print("\n现在我将按空格确认进入")
        input("准备好后按回车...")
        
        self.send_key('space')
        time.sleep(5)
        
        # 保存进入地下城的截图
        self.save_screenshot("in_dungeon")
        print("已保存地下城截图")
        
        choice = input("是否成功进入地下城? (y/n): ").strip().lower()
        if choice != 'y':
            print("进入地下城失败，请检查设置")
            return False
        
        print("\n设置完成！现在可以开始自动搬砖")
        return True
    
    def manual_farming_cycle(self):
        """手动指导的搬砖循环"""
        print("\n=== 手动指导搬砖循环 ===")
        print("我将逐步执行搬砖操作，每步都会询问您")
        
        # 1. 移动和攻击测试
        print("\n1. 测试移动和攻击")
        print("我将向右移动并释放技能")
        input("按回车开始...")
        
        # 移动
        for i in range(3):
            print(f"向右移动 {i+1}/3")
            self.send_key('right')
            time.sleep(0.3)
        
        # 攻击
        for i in range(5):
            print(f"释放技能 {i+1}/5")
            self.send_key(self.skill_key)
            time.sleep(0.2)
        
        choice = input("角色是否正常移动和攻击? (y/n): ").strip().lower()
        if choice != 'y':
            print("移动或攻击有问题，请检查")
            return False
        
        # 2. 测试顺图
        print("\n2. 测试顺图")
        print("我将按Alt+Right顺图")
        input("按回车开始...")
        
        # 顺图
        self.send_key('alt')
        time.sleep(0.05)
        self.send_key('right')
        time.sleep(2)
        
        choice = input("是否成功顺图到下一房间? (y/n): ").strip().lower()
        if choice != 'y':
            print("顺图有问题，可能需要调整时机")
            return False
        
        print("\n基础功能测试完成！")
        return True

def main():
    """主函数"""
    print("DNF智能搬砖机器人")
    print("=" * 40)
    print("带有图像识别和状态判断的版本")
    print()
    
    bot = DNFSmartBot()
    
    while True:
        print("\n请选择操作:")
        print("1. 交互式设置")
        print("2. 手动指导搬砖测试")
        print("3. 截取当前画面")
        print("4. 测试按键")
        print("0. 退出")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            bot.interactive_setup()
        elif choice == '2':
            if bot.find_dnf_window():
                bot.manual_farming_cycle()
            else:
                print("未找到DNF窗口")
        elif choice == '3':
            if bot.find_dnf_window():
                filename = bot.save_screenshot("manual_capture")
                if filename:
                    print(f"截图已保存: {filename}")
            else:
                print("未找到DNF窗口")
        elif choice == '4':
            if bot.find_dnf_window():
                key = input("请输入要测试的按键: ").strip()
                if key:
                    bot.send_key(key)
            else:
                print("未找到DNF窗口")
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
