#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF最终解决方案
基于成功的开源项目经验，使用多种底层输入方法
"""

import ctypes
import time
import os
import configparser
from ctypes import wintypes, Structure, Union, POINTER, byref

# 扫描码映射（硬件级别）
SCAN_CODES = {
    'a': 0x1E, 'b': 0x30, 'c': 0x2E, 'd': 0x20, 'e': 0x12, 'f': 0x21,
    'g': 0x22, 'h': 0x23, 'i': 0x17, 'j': 0x24, 'k': 0x25, 'l': 0x26,
    'm': 0x32, 'n': 0x31, 'o': 0x18, 'p': 0x19, 'q': 0x10, 'r': 0x13,
    's': 0x1F, 't': 0x14, 'u': 0x16, 'v': 0x2F, 'w': 0x11, 'x': 0x2D,
    'y': 0x15, 'z': 0x2C,
    '0': 0x0B, '1': 0x02, '2': 0x03, '3': 0x04, '4': 0x05,
    '5': 0x06, '6': 0x07, '7': 0x08, '8': 0x09, '9': 0x0A,
    'space': 0x39, 'enter': 0x1C, 'esc': 0x01, 'tab': 0x0F,
    'shift': 0x2A, 'ctrl': 0x1D, 'alt': 0x38,
    'left': 0x4B, 'up': 0x48, 'right': 0x4D, 'down': 0x50,
}

class DNFFinalBot:
    """DNF最终搬砖机器人"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.game_hwnd = None
        self.game_pid = None
        self.skill_key = 'e'
        self.running = False
        
        # 加载配置
        self.load_config()
        
        # 启用调试权限
        self.enable_debug_privilege()
    
    def load_config(self):
        """加载配置"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            print(f"技能键设置: {self.skill_key}")
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                print("✓ 调试权限已启用")
        except:
            print("⚠ 调试权限启用失败")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        pid = wintypes.DWORD()
                        self.user32.GetWindowThreadProcessId(hwnd, byref(pid))
                        self.game_hwnd = hwnd
                        self.game_pid = pid.value
                        print(f"找到DNF: {window_title} (PID: {self.game_pid})")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        return self.game_hwnd is not None
    
    def send_key_advanced(self, key, duration=0.05):
        """高级按键发送（使用已验证有效的方法）"""
        scan_code = SCAN_CODES.get(key.lower())
        if not scan_code:
            print(f"未知按键: {key}")
            return False

        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.05)

        # 使用已验证有效的驱动级方法
        try:
            # 方法1: 扫描码 + SendInput（优先使用）
            class KEYBDINPUT(Structure):
                _fields_ = [("wVk", wintypes.WORD), ("wScan", wintypes.WORD),
                           ("dwFlags", wintypes.DWORD), ("time", wintypes.DWORD),
                           ("dwExtraInfo", POINTER(wintypes.ULONG))]

            class INPUT_UNION(Union):
                _fields_ = [("ki", KEYBDINPUT)]

            class INPUT(Structure):
                _fields_ = [("type", wintypes.DWORD), ("ii", INPUT_UNION)]

            inputs = (INPUT * 2)()

            # 按下
            inputs[0].type = 1
            inputs[0].ii.ki.wVk = 0
            inputs[0].ii.ki.wScan = scan_code
            inputs[0].ii.ki.dwFlags = 0x0008  # KEYEVENTF_SCANCODE
            inputs[0].ii.ki.time = 0
            inputs[0].ii.ki.dwExtraInfo = None

            # 释放
            inputs[1].type = 1
            inputs[1].ii.ki.wVk = 0
            inputs[1].ii.ki.wScan = scan_code
            inputs[1].ii.ki.dwFlags = 0x0008 | 0x0002
            inputs[1].ii.ki.time = 0
            inputs[1].ii.ki.dwExtraInfo = None

            result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
            if result == 2:
                return True
        except Exception as e:
            print(f"SendInput失败: {e}")

        # 备用方法: keybd_event with scan code
        try:
            self.user32.keybd_event(0, scan_code, 0x0008, 0)
            time.sleep(duration)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
            return True
        except Exception as e:
            print(f"keybd_event失败: {e}")

        return False

    def send_combination(self, keys):
        """发送组合键"""
        scan_codes = []
        for key in keys:
            scan_code = SCAN_CODES.get(key.lower())
            if scan_code:
                scan_codes.append(scan_code)
            else:
                print(f"未知按键: {key}")
                return False

        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.05)

        try:
            # 按下所有键
            for scan_code in scan_codes:
                self.user32.keybd_event(0, scan_code, 0x0008, 0)
                time.sleep(0.01)

            time.sleep(0.05)

            # 释放所有键（逆序）
            for scan_code in reversed(scan_codes):
                self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
                time.sleep(0.01)

            print(f"✓ 组合键: {'+'.join(keys)}")
            return True
        except Exception as e:
            print(f"组合键失败: {e}")
            return False

    def farming_cycle(self):
        """搬砖循环"""
        print("\n=== 开始搬砖循环 ===")
        
        # 1. 进入地图
        print("1. 进入地图选择")
        self.send_key_advanced('right')
        time.sleep(2)
        
        print("2. 确认进入")
        self.send_key_advanced('space')
        time.sleep(3)
        
        # 3. 打怪循环
        for room in range(8):
            print(f"3.{room+1} 清理房间 {room+1}")
            
            # 移动
            print("  - 向右移动")
            for _ in range(3):  # 多次移动确保到位
                self.send_key_advanced('right')
                time.sleep(0.3)
            
            # 攻击
            print(f"  - 释放技能 {self.skill_key}")
            for _ in range(6):  # 多次攻击确保清空
                self.send_key_advanced(self.skill_key)
                time.sleep(0.2)
            
            # 等待
            time.sleep(1.5)
            
            # 顺图
            if room < 7:
                print("  - 顺图")
                self.send_combination(['alt', 'right'])
                time.sleep(2)
        
        # 4. 退出
        print("4. 退出地图")
        for i in range(3):
            print(f"  - ESC {i+1}")
            self.send_key_advanced('esc')
            time.sleep(1)
        
        print("5. 按数字4")
        self.send_key_advanced('4')
        time.sleep(2)
        
        print("=== 搬砖循环完成 ===\n")
    
    def start_bot(self):
        """启动机器人"""
        if not self.find_dnf_window():
            print("错误: 未找到DNF窗口")
            return
        
        print("DNF搬砖机器人启动")
        print("按Ctrl+C停止")
        
        self.running = True
        cycle = 0
        
        try:
            while self.running:
                cycle += 1
                print(f"\n第 {cycle} 轮搬砖")
                self.farming_cycle()
                time.sleep(3)
        except KeyboardInterrupt:
            print("\n用户停止")
        except Exception as e:
            print(f"\n异常: {e}")
        finally:
            self.running = False

def main():
    """主函数"""
    print("DNF最终解决方案")
    print("=" * 30)
    print("基于成功开源项目的多重输入方法")
    print()
    
    # 检查管理员权限
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            print("⚠ 建议以管理员权限运行")
    except:
        pass
    
    bot = DNFFinalBot()
    
    while True:
        print("\n选择操作:")
        print("1. 启动自动搬砖")
        print("2. 测试按键")
        print("3. 单次循环测试")
        print("0. 退出")
        
        choice = input("\n请选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            print("\n请确保:")
            print("- DNF角色在城镇")
            print("- 没有对话框")
            print("- 准备开始搬砖")
            input("\n按回车开始...")
            bot.start_bot()
        elif choice == '2':
            if not bot.find_dnf_window():
                print("未找到DNF窗口")
                continue
            key = input("输入测试按键: ").strip()
            if key:
                bot.send_key_advanced(key)
        elif choice == '3':
            if not bot.find_dnf_window():
                print("未找到DNF窗口")
                continue
            bot.farming_cycle()
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
