#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF窗口检测工具
用于查找和确认DNF游戏窗口
"""

import ctypes
from ctypes import wintypes

def find_all_windows():
    """查找所有可见窗口"""
    print("正在搜索所有可见窗口...")
    print("=" * 60)
    
    windows = []
    
    def enum_windows_proc(hwnd, lParam):
        user32 = ctypes.windll.user32
        if user32.IsWindowVisible(hwnd):
            length = user32.GetWindowTextLengthW(hwnd)
            if length > 0:
                buffer = ctypes.create_unicode_buffer(length + 1)
                user32.GetWindowTextW(hwnd, buffer, length + 1)
                window_title = buffer.value
                
                # 获取窗口类名
                class_buffer = ctypes.create_unicode_buffer(256)
                user32.GetClassNameW(hwnd, class_buffer, 256)
                class_name = class_buffer.value
                
                windows.append((hwnd, window_title, class_name))
        return True
    
    EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
    ctypes.windll.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
    
    # 显示所有窗口
    for i, (hwnd, title, class_name) in enumerate(windows, 1):
        print(f"{i:3d}. 窗口句柄: {hwnd}")
        print(f"     标题: {title}")
        print(f"     类名: {class_name}")
        print()
    
    return windows

def find_dnf_windows():
    """专门查找DNF相关窗口"""
    print("正在搜索DNF相关窗口...")
    print("=" * 60)
    
    dnf_windows = []
    
    def enum_windows_proc(hwnd, lParam):
        user32 = ctypes.windll.user32
        if user32.IsWindowVisible(hwnd):
            length = user32.GetWindowTextLengthW(hwnd)
            if length > 0:
                buffer = ctypes.create_unicode_buffer(length + 1)
                user32.GetWindowTextW(hwnd, buffer, length + 1)
                window_title = buffer.value
                
                # 检查是否包含DNF相关关键词
                keywords = ["dnf", "dungeon", "fighter", "地下城", "勇士"]
                if any(keyword.lower() in window_title.lower() for keyword in keywords):
                    # 获取窗口类名
                    class_buffer = ctypes.create_unicode_buffer(256)
                    user32.GetClassNameW(hwnd, class_buffer, 256)
                    class_name = class_buffer.value
                    
                    dnf_windows.append((hwnd, window_title, class_name))
        return True
    
    EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
    ctypes.windll.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
    
    if dnf_windows:
        print("找到以下DNF相关窗口:")
        for i, (hwnd, title, class_name) in enumerate(dnf_windows, 1):
            print(f"{i}. 窗口句柄: {hwnd}")
            print(f"   标题: {title}")
            print(f"   类名: {class_name}")
            print()
    else:
        print("未找到DNF相关窗口")
        print("请确保:")
        print("1. DNF游戏已启动")
        print("2. 游戏窗口没有最小化")
        print("3. 游戏已完全加载")
    
    return dnf_windows

def test_window_activation(hwnd):
    """测试窗口激活"""
    try:
        user32 = ctypes.windll.user32
        user32.SetForegroundWindow(hwnd)
        user32.ShowWindow(hwnd, 9)  # SW_RESTORE
        print(f"成功激活窗口: {hwnd}")
        return True
    except Exception as e:
        print(f"激活窗口失败: {e}")
        return False

def main():
    """主函数"""
    print("DNF窗口检测工具")
    print("=" * 30)
    
    while True:
        print("\n请选择操作:")
        print("1. 查找DNF窗口")
        print("2. 查看所有窗口")
        print("3. 测试窗口激活")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            dnf_windows = find_dnf_windows()
            if dnf_windows:
                print(f"\n✓ 找到 {len(dnf_windows)} 个DNF相关窗口")
                # 保存第一个窗口信息供测试使用
                if len(dnf_windows) == 1:
                    global test_hwnd
                    test_hwnd = dnf_windows[0][0]
            else:
                print("\n✗ 未找到DNF窗口")
        elif choice == '2':
            windows = find_all_windows()
            print(f"\n找到 {len(windows)} 个可见窗口")
        elif choice == '3':
            if 'test_hwnd' in globals():
                print(f"测试激活窗口: {test_hwnd}")
                test_window_activation(test_hwnd)
            else:
                print("请先查找DNF窗口")
        else:
            print("无效选择")
    
    print("程序结束")

if __name__ == "__main__":
    main()
