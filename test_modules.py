#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块测试脚本
用于测试各个模块的基本功能
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.config_manager import ConfigManager
from modules.input_simulator import InputSimulator
from modules.image_detector import ImageDetector
from modules.game_controller import GameController
from modules.logger import setup_logger

def test_config_manager():
    """测试配置管理器"""
    print("=" * 50)
    print("测试配置管理器")
    print("=" * 50)
    
    config = ConfigManager()
    
    print(f"工作时长: {config.get_work_hours()}小时")
    print(f"技能键: {config.get_skill_key()}")
    print(f"使用摆摊分解: {config.use_stall_decompose()}")
    print(f"开启深渊: {config.enable_abyss()}")
    print(f"背包满度阈值: {config.get_bag_full_threshold()}%")
    print(f"图像识别阈值: {config.get_image_threshold()}")
    
    hotkeys = config.get_all_hotkeys()
    print("快捷键配置:")
    for key, value in hotkeys.items():
        print(f"  {key}: {value}")
    
    print("配置管理器测试完成\n")

def test_input_simulator():
    """测试输入模拟器"""
    print("=" * 50)
    print("测试输入模拟器")
    print("=" * 50)
    
    input_sim = InputSimulator()
    
    # 测试查找游戏窗口
    if input_sim.find_game_window():
        print("✓ 找到游戏窗口")
    else:
        print("✗ 未找到游戏窗口")
    
    # 测试按键解析
    hotkey = "alt+q"
    keys = input_sim.parse_hotkey(hotkey)
    print(f"热键解析 '{hotkey}': {keys}")
    
    print("输入模拟器测试完成\n")

def test_image_detector():
    """测试图像检测器"""
    print("=" * 50)
    print("测试图像检测器")
    print("=" * 50)
    
    detector = ImageDetector()
    
    # 检查模板图像加载情况
    print(f"已加载模板图像数量: {len(detector.template_cache)}")
    for name in detector.template_cache.keys():
        print(f"  - {name}")
    
    # 测试查找游戏窗口
    if detector.find_game_window():
        print("✓ 找到游戏窗口")
        
        # 测试截屏功能
        screen = detector.capture_screen()
        if screen is not None:
            print(f"✓ 截屏成功，尺寸: {screen.shape}")
        else:
            print("✗ 截屏失败")
    else:
        print("✗ 未找到游戏窗口")
    
    print("图像检测器测试完成\n")

def test_game_controller():
    """测试游戏控制器"""
    print("=" * 50)
    print("测试游戏控制器")
    print("=" * 50)
    
    config = ConfigManager()
    input_sim = InputSimulator()
    image_detector = ImageDetector()
    
    controller = GameController(input_sim, image_detector, config)
    
    print(f"技能键设置: {controller.skill_key}")
    print("游戏控制器初始化完成")
    
    # 测试状态检测
    current_state = controller.state_detector.get_current_state(force_update=True)
    print(f"当前游戏状态: {current_state.value}")
    
    print("游戏控制器测试完成\n")

def test_logger():
    """测试日志系统"""
    print("=" * 50)
    print("测试日志系统")
    print("=" * 50)
    
    logger = setup_logger("TestLogger")
    
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    
    print("日志系统测试完成\n")

def interactive_test():
    """交互式测试"""
    print("=" * 50)
    print("交互式测试")
    print("=" * 50)
    
    while True:
        print("\n请选择要测试的功能:")
        print("1. 截取当前屏幕")
        print("2. 检测游戏状态")
        print("3. 测试按键模拟")
        print("4. 保存配置")
        print("0. 退出")
        
        choice = input("请输入选择 (0-4): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            test_screenshot()
        elif choice == '2':
            test_game_state_detection()
        elif choice == '3':
            test_key_simulation()
        elif choice == '4':
            test_config_save()
        else:
            print("无效选择，请重新输入")

def test_screenshot():
    """测试截屏功能"""
    detector = ImageDetector()
    if detector.find_game_window():
        detector.save_screenshot("test_screenshot.png")
        print("截图已保存为 test_screenshot.png")
    else:
        print("未找到游戏窗口")

def test_game_state_detection():
    """测试游戏状态检测"""
    config = ConfigManager()
    detector = ImageDetector()
    controller = GameController(None, detector, config)
    
    state = controller.state_detector.get_current_state(force_update=True)
    print(f"检测到的游戏状态: {state.value}")
    
    print("详细检测结果:")
    print(f"  在城镇: {detector.is_in_town()}")
    print(f"  在地下城: {detector.is_in_dungeon()}")
    print(f"  地图选择界面: {detector.is_map_selection_open()}")
    print(f"  翻牌界面: {detector.is_flip_card_open()}")

def test_key_simulation():
    """测试按键模拟"""
    input_sim = InputSimulator()
    
    print("将在3秒后测试按键模拟...")
    time.sleep(3)
    
    if input_sim.find_game_window():
        print("测试按下ESC键")
        input_sim.press_key('esc')
        print("按键测试完成")
    else:
        print("未找到游戏窗口")

def test_config_save():
    """测试配置保存"""
    config = ConfigManager()
    
    # 修改一个配置项
    config.update_config('GENERAL', 'work_hours', '10')
    print("配置已更新: work_hours = 10")
    
    # 重新加载配置
    config.load_config()
    print(f"重新加载后的工作时长: {config.get_work_hours()}小时")

def main():
    """主函数"""
    print("DNF搬砖脚本模块测试")
    print("=" * 50)
    
    # 创建必要的目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("images", exist_ok=True)
    
    try:
        # 基础模块测试
        test_logger()
        test_config_manager()
        test_input_simulator()
        test_image_detector()
        test_game_controller()
        
        # 交互式测试
        interactive_test()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    
    print("测试完成")

if __name__ == "__main__":
    main()
