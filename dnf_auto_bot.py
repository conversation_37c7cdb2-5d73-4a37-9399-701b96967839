#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF全自动搬砖机器人
基于图像识别和正确的游戏机制
"""

import ctypes
import time
import os
import cv2
import numpy as np
from PIL import ImageGrab
import configparser
import logging
from datetime import datetime
from ctypes import wintypes, byref

# 扫描码（使用验证有效的版本）
HARDWARE_SCAN_CODES = {
    'e': 0x12, 'space': 0x39, 'esc': 0x01, '4': 0x05,
    'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0,
    'alt': 0x38, 'f11': 0x57, '0': 0x0B,
    'a': 0x1E, 's': 0x1F, 'd': 0x20, 'w': 0x11,
}

class DNFAutoBot:
    """DNF全自动搬砖机器人"""
    
    def __init__(self):
        self.kernel32 = ctypes.windll.kernel32
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.window_rect = None
        
        # 配置
        self.skill_key = 'e'
        self.enable_abyss = False
        self.work_hours = 8
        
        # 运行状态
        self.running = False
        self.start_time = None
        
        # 模板缓存
        self.templates = {}
        
        # 初始化
        self.setup_logging()
        self.load_config()
        self.enable_debug_privilege()
        self.load_templates()
    
    def setup_logging(self):
        """设置日志"""
        os.makedirs("logs", exist_ok=True)
        log_file = f"logs/dnf_auto_bot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """加载配置"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            self.enable_abyss = config.getboolean('FEATURES', 'enable_abyss', fallback=False)
            self.work_hours = config.getint('GENERAL', 'work_hours', fallback=8)
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                self.logger.info("调试权限已启用")
        except Exception as e:
            self.logger.warning(f"调试权限启用失败: {e}")
    
    def load_templates(self):
        """加载所有模板"""
        template_dir = "templates"
        if not os.path.exists(template_dir):
            self.logger.error("模板目录不存在")
            return
        
        template_files = [f for f in os.listdir(template_dir) if f.endswith('.png')]
        
        for template_file in template_files:
            template_name = template_file[:-4]  # 去掉.png后缀
            template_path = os.path.join(template_dir, template_file)
            template = cv2.imread(template_path)
            
            if template is not None:
                self.templates[template_name] = template
                self.logger.debug(f"加载模板: {template_name}")
            else:
                self.logger.warning(f"无法加载模板: {template_file}")
        
        self.logger.info(f"共加载 {len(self.templates)} 个模板")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        self.logger.info(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def capture_game_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            self.logger.error(f"截屏失败: {e}")
            return None
    
    def find_template(self, template_name, threshold=0.7):
        """查找模板"""
        if template_name not in self.templates:
            self.logger.warning(f"模板不存在: {template_name}")
            return None
        
        screen = self.capture_game_screen()
        if screen is None:
            return None
        
        template = self.templates[template_name]
        result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        if max_val > threshold:
            h, w = template.shape[:2]
            center_x = max_loc[0] + w // 2
            center_y = max_loc[1] + h // 2
            self.logger.debug(f"找到模板 {template_name}: 位置({center_x}, {center_y}), 匹配度{max_val:.3f}")
            return (center_x, center_y, max_val)
        
        return None
    
    def send_key(self, key):
        """发送按键"""
        scan_code = HARDWARE_SCAN_CODES.get(key.lower())
        if not scan_code:
            self.logger.error(f"未知按键: {key}")
            return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            self.logger.debug(f"发送按键: {key}")
            return True
        except Exception as e:
            self.logger.error(f"发送按键失败: {e}")
            return False
    
    def send_combination(self, keys):
        """发送组合键"""
        scan_codes = []
        for key in keys:
            scan_code = HARDWARE_SCAN_CODES.get(key.lower())
            if scan_code:
                scan_codes.append(scan_code)
            else:
                self.logger.error(f"未知按键: {key}")
                return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 按下所有键
            for scan_code in scan_codes:
                self.user32.keybd_event(0, scan_code, 0x0008, 0)
                time.sleep(0.01)
            
            time.sleep(0.05)
            
            # 释放所有键（逆序）
            for scan_code in reversed(scan_codes):
                self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
                time.sleep(0.01)
            
            self.logger.debug(f"发送组合键: {'+'.join(keys)}")
            return True
        except Exception as e:
            self.logger.error(f"发送组合键失败: {e}")
            return False
    
    def detect_room_flicker(self):
        """检测房间格子闪烁"""
        # 连续截取两张图片检测亮度变化
        images = []
        for _ in range(2):
            # 查找下个房间格子
            next_room_pos = self.find_template("minimap_next_room_bright")
            if next_room_pos:
                screen = self.capture_game_screen()
                if screen is not None:
                    # 提取房间格子区域
                    template = self.templates["minimap_next_room_bright"]
                    h, w = template.shape[:2]
                    x, y = next_room_pos[0] - w//2, next_room_pos[1] - h//2
                    room_region = screen[y:y+h, x:x+w]
                    images.append(room_region)
            time.sleep(0.3)
        
        if len(images) >= 2:
            # 计算亮度差异
            gray1 = cv2.cvtColor(images[0], cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(images[1], cv2.COLOR_BGR2GRAY)
            
            brightness1 = np.mean(gray1)
            brightness2 = np.mean(gray2)
            brightness_diff = abs(brightness1 - brightness2)
            
            # 如果亮度差异大于阈值，说明在闪烁
            is_flickering = brightness_diff > 20
            self.logger.debug(f"房间格子闪烁检测: 差异={brightness_diff:.2f}, 闪烁={is_flickering}")
            return is_flickering
        
        return False
    
    def is_room_clear(self):
        """检查房间是否清空"""
        # 方法1: 检测房间格子闪烁
        if self.detect_room_flicker():
            return True
        
        # 方法2: 检查是否有亮的房间格子
        if self.find_template("minimap_next_room_bright"):
            return True
        
        # 方法3: 检查是否没有暗的房间格子
        if not self.find_template("minimap_next_room_dark"):
            return True
        
        return False
    
    def wait_for_state_change(self, check_func, timeout=10, check_interval=0.5):
        """等待状态变化"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if check_func():
                return True
            time.sleep(check_interval)
        
        return False

    def enter_grandi_dungeon(self):
        """进入格兰迪地下城"""
        self.logger.info("开始进入格兰迪地下城")

        # 1. 检查是否在城镇
        if not self.find_template("town_interface"):
            self.logger.warning("不在城镇，尝试返回城镇")
            return False

        # 2. 进入地图选择
        self.logger.info("进入地图选择")
        self.send_key('right')

        # 等待地图选择界面出现
        if not self.wait_for_state_change(lambda: self.find_template("map_selection_interface"), timeout=5):
            self.logger.error("未能进入地图选择界面")
            return False

        # 3. 选择格兰迪
        self.logger.info("选择格兰迪")
        grandi_pos = self.find_template("grandi_option")
        if grandi_pos:
            time.sleep(0.5)
        else:
            self.logger.warning("未找到格兰迪选项")

        # 4. 选择地狱难度
        self.logger.info("选择地狱难度")
        hell_pos = self.find_template("hell_difficulty")
        if hell_pos:
            time.sleep(0.5)
        else:
            self.logger.warning("未找到地狱难度选项")

        # 5. 开启深渊模式（如果启用）
        if self.enable_abyss:
            self.logger.info("开启深渊模式")
            self.send_key('f11')
            time.sleep(0.5)

        # 6. 确认进入
        self.logger.info("确认进入地下城")
        self.send_key('space')

        # 等待进入地下城
        if self.wait_for_state_change(lambda: self.find_template("in_dungeon_interface"), timeout=10):
            self.logger.info("成功进入格兰迪地下城")
            return True
        else:
            self.logger.error("进入地下城失败")
            return False

    def handle_abyss_room(self):
        """处理深渊房间"""
        self.logger.info("检测到深渊房间，开始深渊流程")

        wave_count = 0
        max_waves = 3

        while wave_count < max_waves:
            wave_count += 1
            self.logger.info(f"深渊第 {wave_count}/{max_waves} 波")

            # 1. 寻找并激活柱子
            if self.find_and_activate_pillar():
                self.logger.info("柱子已激活，等待刷怪")
                time.sleep(2)  # 等待怪物刷新

                # 2. 清理这波怪物
                if self.clear_current_wave():
                    self.logger.info(f"第{wave_count}波清理完成")
                else:
                    self.logger.warning(f"第{wave_count}波清理可能不完全")

                # 3. 等待下一波（如果不是最后一波）
                if wave_count < max_waves:
                    self.logger.info("等待下一波...")
                    time.sleep(3)
            else:
                self.logger.warning(f"第{wave_count}波柱子激活失败")

        # 4. 检查深渊是否完成
        if self.check_abyss_complete():
            self.logger.info("深渊房间完成")
            return True
        else:
            self.logger.warning("深渊房间可能未完全完成")
            return False

    def find_and_activate_pillar(self):
        """寻找并激活柱子"""
        # 1. 寻找正常状态的柱子
        pillar_pos = self.find_template("abyss_pillar_normal")

        if pillar_pos:
            self.logger.info("找到正常状态柱子，开始攻击")

            # 2. 攻击柱子直到激活
            for attempt in range(15):
                self.send_key(self.skill_key)
                time.sleep(0.3)

                # 检查柱子是否已激活（发白光）
                if self.find_template("abyss_pillar_activated"):
                    self.logger.info("柱子已激活（发白光）")
                    return True

            self.logger.warning("柱子激活失败")
            return False
        else:
            # 可能柱子已经被激活
            if self.find_template("abyss_pillar_activated"):
                self.logger.info("柱子已处于激活状态")
                return True

            self.logger.warning("未找到深渊柱子")
            return False

    def clear_current_wave(self):
        """清理当前波的怪物"""
        self.logger.info("开始清理当前波怪物")

        max_clear_time = 30  # 最多清理30秒
        start_time = time.time()

        while time.time() - start_time < max_clear_time:
            # 检查房间是否还有怪物
            if self.is_room_clear():
                self.logger.info("当前波怪物已清理完成")
                return True

            # 继续攻击和移动
            self.attack_and_move()
            time.sleep(0.5)

        self.logger.warning("清理怪物超时")
        return False

    def attack_and_move(self):
        """攻击和移动"""
        # 释放技能
        self.send_key(self.skill_key)
        time.sleep(0.2)

        # 随机移动确保覆盖房间
        import random
        move_keys = ['right', 'left', 'up', 'down']
        move_key = random.choice(move_keys)
        self.send_key(move_key)
        time.sleep(0.1)

    def check_abyss_complete(self):
        """检查深渊是否完成"""
        # 方法1: 检查深渊完成特效
        if self.find_template("abyss_complete_effect"):
            return True

        # 方法2: 检查房间格子状态
        if self.is_room_clear():
            return True

        # 方法3: 等待一段时间后再次检查
        time.sleep(2)
        return self.is_room_clear()

    def clear_normal_room(self):
        """清理普通房间"""
        self.logger.info("清理普通房间")

        max_clear_time = 20  # 普通房间最多20秒
        start_time = time.time()

        while time.time() - start_time < max_clear_time:
            # 检查房间是否清空
            if self.is_room_clear():
                self.logger.info("普通房间清理完成")
                return True

            # 攻击和移动
            self.attack_and_move()
            time.sleep(0.3)

        self.logger.warning("普通房间清理超时")
        return False

    def move_to_next_room(self):
        """移动到下一房间"""
        self.logger.info("移动到下一房间")

        # 使用Alt+Right顺图
        if self.send_combination(['alt', 'right']):
            # 等待移动完成
            time.sleep(2)

            # 检查是否成功移动
            if self.find_template("in_dungeon_interface"):
                self.logger.info("成功移动到下一房间")
                return True

        self.logger.warning("移动到下一房间失败")
        return False

    def clear_dungeon(self):
        """清理整个地下城"""
        self.logger.info("开始清理地下城")

        for room_num in range(8):  # 格兰迪通常8个房间
            self.logger.info(f"清理房间 {room_num + 1}/8")

            # 检查是否是深渊房间
            if self.enable_abyss and (self.find_template("abyss_pillar_normal") or self.find_template("abyss_pillar_activated")):
                # 处理深渊房间
                if not self.handle_abyss_room():
                    self.logger.warning(f"深渊房间 {room_num + 1} 处理失败")
            else:
                # 处理普通房间
                if not self.clear_normal_room():
                    self.logger.warning(f"普通房间 {room_num + 1} 清理失败")

            # 移动到下一房间（除了最后一个房间）
            if room_num < 7:
                if not self.move_to_next_room():
                    self.logger.error(f"无法移动到房间 {room_num + 2}")
                    return False

        self.logger.info("地下城清理完成")
        return True

    def exit_dungeon(self):
        """退出地下城"""
        self.logger.info("退出地下城")

        # 等待可能的翻牌或结算界面
        time.sleep(3)

        # 检查是否有翻牌界面
        if self.find_template("flip_card"):
            self.logger.info("检测到翻牌界面")
            time.sleep(2)

        # 多次按ESC确保退出
        for i in range(5):
            self.logger.debug(f"按ESC第{i+1}次")
            self.send_key('esc')
            time.sleep(1.5)

            # 检查是否回到城镇
            if self.find_template("town_interface"):
                self.logger.info("成功返回城镇")
                return True

        # 如果还没回到城镇，尝试其他方法
        self.logger.info("尝试其他退出方法")
        self.send_key('4')  # 按数字键4
        time.sleep(2)

        # 最终检查
        if self.find_template("town_interface"):
            self.logger.info("成功返回城镇")
            return True
        else:
            self.logger.error("退出地下城失败")
            return False

    def handle_inventory(self):
        """处理背包"""
        self.logger.info("处理背包")

        # 检查是否背包满
        if self.find_template("inventory_full"):
            self.logger.info("背包已满，开始处理")

            # 使用摆摊分解
            self.send_key('0')
            time.sleep(3)

            # 分解装备
            self.send_combination(['alt', 'd'])
            time.sleep(2)

            # 修理装备
            self.send_combination(['alt', 'w'])
            time.sleep(1.5)

        return True

    def farming_cycle(self):
        """完整的搬砖循环"""
        cycle_start = time.time()
        self.logger.info("=" * 50)
        self.logger.info("开始搬砖循环")

        try:
            # 1. 进入格兰迪地下城
            if not self.enter_grandi_dungeon():
                self.logger.error("进入地下城失败")
                return False

            # 2. 清理地下城
            if not self.clear_dungeon():
                self.logger.error("清理地下城失败")
                return False

            # 3. 退出地下城
            if not self.exit_dungeon():
                self.logger.error("退出地下城失败")
                return False

            # 4. 处理背包
            self.handle_inventory()

            cycle_time = time.time() - cycle_start
            self.logger.info(f"搬砖循环完成，耗时: {cycle_time:.1f}秒")
            return True

        except Exception as e:
            self.logger.error(f"搬砖循环异常: {e}")
            return False

    def is_work_time_exceeded(self):
        """检查是否超过工作时长"""
        if self.start_time is None:
            return False

        elapsed = time.time() - self.start_time
        return elapsed >= (self.work_hours * 3600)

    def start_bot(self):
        """启动机器人"""
        if not self.find_dnf_window():
            self.logger.error("未找到DNF窗口，请确保游戏已启动")
            return

        if len(self.templates) == 0:
            self.logger.error("未加载任何模板，请先收集模板")
            return

        self.logger.info("=" * 50)
        self.logger.info("DNF全自动搬砖机器人启动")
        self.logger.info(f"工作时长: {self.work_hours}小时")
        self.logger.info(f"技能键: {self.skill_key}")
        self.logger.info(f"深渊模式: {'开启' if self.enable_abyss else '关闭'}")
        self.logger.info(f"已加载模板: {len(self.templates)}个")
        self.logger.info("按Ctrl+C停止机器人")
        self.logger.info("=" * 50)

        self.running = True
        self.start_time = time.time()
        cycle_count = 0
        success_count = 0

        try:
            while self.running:
                # 检查工作时长
                if self.is_work_time_exceeded():
                    self.logger.info("已达到设定工作时长，机器人停止")
                    break

                cycle_count += 1
                self.logger.info(f"\n第 {cycle_count} 轮搬砖开始")

                # 执行搬砖循环
                if self.farming_cycle():
                    success_count += 1
                    self.logger.info(f"第 {cycle_count} 轮搬砖成功")
                else:
                    self.logger.warning(f"第 {cycle_count} 轮搬砖失败")

                # 等待下一轮
                self.logger.info("等待下一轮...")
                time.sleep(5)

        except KeyboardInterrupt:
            self.logger.info("用户中断机器人")
        except Exception as e:
            self.logger.error(f"机器人运行异常: {e}")
        finally:
            self.running = False
            total_time = time.time() - self.start_time if self.start_time else 0
            self.logger.info(f"机器人已停止")
            self.logger.info(f"总运行时间: {total_time/3600:.1f}小时")
            self.logger.info(f"总循环次数: {cycle_count}")
            self.logger.info(f"成功次数: {success_count}")
            if cycle_count > 0:
                self.logger.info(f"成功率: {success_count/cycle_count*100:.1f}%")

def main():
    """主函数"""
    print("DNF全自动搬砖机器人")
    print("=" * 40)
    print("基于图像识别和正确的游戏机制")
    print()

    # 检查管理员权限
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            print("⚠ 建议以管理员权限运行")
    except:
        pass

    bot = DNFAutoBot()

    while True:
        print("\n请选择操作:")
        print("1. 启动全自动搬砖")
        print("2. 测试模板识别")
        print("3. 单次搬砖测试")
        print("4. 查看配置和模板")
        print("0. 退出")

        choice = input("\n请输入选择: ").strip()

        if choice == '0':
            break
        elif choice == '1':
            print("\n请确保:")
            print("- DNF角色在城镇中")
            print("- 面向地图传送门")
            print("- 没有对话框或菜单打开")
            print("- 有足够的疲劳值")
            print("- 已收集所需的图像模板")
            input("\n确认无误后按回车开始...")
            bot.start_bot()
        elif choice == '2':
            if not bot.find_dnf_window():
                print("未找到DNF窗口")
                continue

            print("测试模板识别...")
            test_templates = ["town_interface", "grandi_option", "hell_difficulty",
                            "minimap_next_room_dark", "minimap_next_room_bright"]

            for template_name in test_templates:
                result = bot.find_template(template_name)
                if result:
                    print(f"✓ {template_name}: 找到，匹配度 {result[2]:.3f}")
                else:
                    print(f"✗ {template_name}: 未找到")
        elif choice == '3':
            if not bot.find_dnf_window():
                print("未找到DNF窗口")
                continue
            print("执行单次搬砖测试...")
            bot.farming_cycle()
        elif choice == '4':
            print(f"\n当前配置:")
            print(f"技能键: {bot.skill_key}")
            print(f"工作时长: {bot.work_hours}小时")
            print(f"深渊模式: {'开启' if bot.enable_abyss else '关闭'}")
            print(f"已加载模板: {len(bot.templates)}个")

            if bot.templates:
                print("\n模板列表:")
                for template_name in sorted(bot.templates.keys()):
                    print(f"  ✓ {template_name}")
        else:
            print("无效选择")

    print("程序结束")

if __name__ == "__main__":
    main()
