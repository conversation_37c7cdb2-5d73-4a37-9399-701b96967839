#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
背包管理和装备处理模块
负责背包检测、摆摊分解、装备修理等功能
"""

import time
import logging
from typing import Optional, Tuple

class InventoryManager:
    """背包管理器"""
    
    def __init__(self, input_simulator, image_detector, config):
        self.input_sim = input_simulator
        self.image_detector = image_detector
        self.config = config
        self.logger = logging.getLogger("InventoryManager")
        
        self.bag_full_threshold = config.get_bag_full_threshold()
    
    def check_bag_fullness(self) -> int:
        """检查背包满度百分比"""
        try:
            # 打开背包
            if not self.open_bag():
                return 0
            
            # 这里需要实现具体的背包满度检测逻辑
            # 可以通过图像识别背包格子的占用情况
            # 或者检测特定的满背包提示
            
            # 暂时使用简单的检测方法
            if self.image_detector.is_bag_full():
                return 100
            
            # 关闭背包
            self.close_bag()
            
            # 这里返回一个估计值，实际应该通过图像分析计算
            return 50  # 临时返回值
            
        except Exception as e:
            self.logger.error(f"检查背包满度失败: {e}")
            return 0
    
    def is_bag_nearly_full(self) -> bool:
        """检查背包是否接近满了"""
        fullness = self.check_bag_fullness()
        return fullness >= self.bag_full_threshold
    
    def open_bag(self) -> bool:
        """打开背包"""
        try:
            self.input_sim.press_key('i')
            time.sleep(0.5)
            return True
        except Exception as e:
            self.logger.error(f"打开背包失败: {e}")
            return False
    
    def close_bag(self) -> bool:
        """关闭背包"""
        try:
            self.input_sim.press_key('esc')
            time.sleep(0.3)
            return True
        except Exception as e:
            self.logger.error(f"关闭背包失败: {e}")
            return False
    
    def stall_decompose(self) -> bool:
        """摆摊分解"""
        try:
            self.logger.info("开始摆摊分解")
            
            # 按0键打开摆摊分解机
            self.input_sim.press_key('0')
            time.sleep(1.0)
            
            # 检查是否成功摆摊（地面是否变绿）
            # 这里需要通过图像识别检测地面颜色变化
            # 暂时使用延时等待
            time.sleep(2.0)
            
            # 检查分解机耐久度
            if not self.check_decompose_machine_durability():
                self.logger.warning("分解机耐久度不足，需要修理")
                return False
            
            # 打开分解面板
            if not self.open_decompose_panel():
                return False
            
            # 执行分解
            self.direct_decompose()
            
            # 关闭分解面板
            self.close_decompose_panel()
            
            self.logger.info("摆摊分解完成")
            return True
            
        except Exception as e:
            self.logger.error(f"摆摊分解失败: {e}")
            return False
    
    def direct_decompose(self) -> bool:
        """直接分解装备"""
        try:
            self.logger.info("执行直接分解")
            
            # 按Alt+D进行分解
            self.input_sim.press_key_combination(['alt', 'd'])
            time.sleep(1.0)
            
            # 可能需要确认分解
            # 这里可以添加确认按钮的点击
            
            return True
            
        except Exception as e:
            self.logger.error(f"直接分解失败: {e}")
            return False
    
    def quick_repair_equipment(self) -> bool:
        """快速修理穿戴的装备"""
        try:
            self.logger.info("执行快速修理装备")
            
            # 按Alt+W进行快速修理
            self.input_sim.press_key_combination(['alt', 'w'])
            time.sleep(1.0)
            
            return True
            
        except Exception as e:
            self.logger.error(f"快速修理装备失败: {e}")
            return False
    
    def open_decompose_panel(self) -> bool:
        """打开分解面板"""
        try:
            # 这里需要根据实际游戏界面来实现
            # 可能需要点击分解机或使用特定快捷键
            time.sleep(0.5)
            return True
        except Exception as e:
            self.logger.error(f"打开分解面板失败: {e}")
            return False
    
    def close_decompose_panel(self) -> bool:
        """关闭分解面板"""
        try:
            self.input_sim.press_key('esc')
            time.sleep(0.3)
            return True
        except Exception as e:
            self.logger.error(f"关闭分解面板失败: {e}")
            return False
    
    def check_decompose_machine_durability(self) -> bool:
        """检查分解机耐久度"""
        try:
            # 这里需要通过图像识别检测分解机的耐久度
            # 如果耐久度小于0，返回False
            # 暂时返回True
            return True
        except Exception as e:
            self.logger.error(f"检查分解机耐久度失败: {e}")
            return False
    
    def repair_decompose_machine(self) -> bool:
        """修理分解机"""
        try:
            self.logger.info("需要修理分解机")
            
            # 找到修理NPC
            repair_npc_pos = self.image_detector.find_template('repair_npc')
            if repair_npc_pos:
                # 点击修理NPC
                self.input_sim.click_mouse(repair_npc_pos[0], repair_npc_pos[1])
                time.sleep(1.0)
                
                # 这里需要添加修理的具体操作
                # 比如选择修理选项、确认修理等
                
                return True
            else:
                self.logger.warning("未找到修理NPC")
                return False
                
        except Exception as e:
            self.logger.error(f"修理分解机失败: {e}")
            return False
    
    def handle_full_bag(self) -> bool:
        """处理背包满的情况"""
        try:
            self.logger.info("处理背包满的情况")
            
            if self.config.use_stall_decompose():
                # 使用摆摊分解
                if self.stall_decompose():
                    # 分解后修理装备
                    self.quick_repair_equipment()
                    return True
            else:
                # 直接分解（但无法分解沾染异界气息的装备）
                self.direct_decompose()
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理背包满失败: {e}")
            return False
