#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础输入测试 - 最简单的测试
"""

import ctypes
import time
from ctypes import wintypes, byref

class BasicInputTest:
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title} (句柄: {hwnd})")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def test_simple_right(self):
        """最简单的右键测试"""
        print("测试最简单的右键输入...")
        
        # 激活窗口
        print("激活DNF窗口...")
        result = self.user32.SetForegroundWindow(self.game_hwnd)
        print(f"激活结果: {result}")
        time.sleep(0.5)
        
        # 发送右键 - 使用最基础的方法
        print("发送右键...")
        scan_code = 0xCD  # 右键扫描码
        
        # 按下
        self.user32.keybd_event(0, scan_code, 0x0008, 0)
        time.sleep(0.05)
        # 释放
        self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
        
        print("右键已发送，请观察角色是否移动")
    
    def test_window_info(self):
        """测试窗口信息"""
        if not self.game_hwnd:
            print("未找到窗口")
            return
        
        # 获取窗口信息
        rect = wintypes.RECT()
        self.user32.GetWindowRect(self.game_hwnd, byref(rect))
        
        foreground = self.user32.GetForegroundWindow()
        active = self.user32.GetActiveWindow()
        
        print(f"窗口句柄: {self.game_hwnd}")
        print(f"窗口位置: ({rect.left}, {rect.top}) - ({rect.right}, {rect.bottom})")
        print(f"前台窗口: {foreground} (是否为DNF: {foreground == self.game_hwnd})")
        print(f"活动窗口: {active}")
    
    def manual_test(self):
        """手动测试"""
        while True:
            print("\n" + "=" * 30)
            print("基础输入测试")
            print("=" * 30)
            print("1. 测试右键")
            print("2. 测试E键")
            print("3. 查看窗口信息")
            print("4. 激活窗口")
            print("0. 退出")
            
            choice = input("请选择: ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                self.test_simple_right()
            elif choice == '2':
                print("发送E键...")
                self.user32.SetForegroundWindow(self.game_hwnd)
                time.sleep(0.2)
                self.user32.keybd_event(0, 0x12, 0x0008, 0)  # E键扫描码
                time.sleep(0.05)
                self.user32.keybd_event(0, 0x12, 0x0008 | 0x0002, 0)
                print("E键已发送")
            elif choice == '3':
                self.test_window_info()
            elif choice == '4':
                print("激活DNF窗口...")
                self.user32.SetForegroundWindow(self.game_hwnd)
                time.sleep(0.2)
                print("窗口已激活")
            else:
                print("无效选择")

def main():
    tester = BasicInputTest()
    
    if not tester.find_dnf_window():
        print("未找到DNF窗口")
        return
    
    print("\n请确保角色在可以移动的地方")
    print("然后测试按键是否有效")
    
    tester.manual_test()

if __name__ == "__main__":
    main()
