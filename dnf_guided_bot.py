#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF引导式搬砖机器人
通过用户确认来解决状态判断问题
"""

import ctypes
import time
import os
import configparser
from ctypes import wintypes, byref

# 扫描码（使用验证有效的版本）
HARDWARE_SCAN_CODES = {
    'e': 0x12, 'space': 0x39, 'esc': 0x01, '4': 0x05,
    'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0,
    'alt': 0x38, 'f11': 0x57, '0': 0x0B,
}

class DNFGuidedBot:
    """DNF引导式搬砖机器人"""
    
    def __init__(self):
        self.kernel32 = ctypes.windll.kernel32
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.game_pid = None
        
        # 配置
        self.skill_key = 'e'
        self.enable_abyss = False
        self.use_stall_decompose = True
        
        self.load_config()
        self.enable_debug_privilege()
    
    def load_config(self):
        """加载配置"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            self.enable_abyss = config.getboolean('FEATURES', 'enable_abyss', fallback=False)
            self.use_stall_decompose = config.getboolean('FEATURES', 'use_stall_decompose', fallback=True)
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                print("✓ 调试权限已启用")
        except Exception as e:
            print(f"调试权限启用失败: {e}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        pid = wintypes.DWORD()
                        self.user32.GetWindowThreadProcessId(hwnd, byref(pid))
                        
                        self.game_hwnd = hwnd
                        self.game_pid = pid.value
                        print(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def send_key(self, key):
        """发送按键"""
        scan_code = HARDWARE_SCAN_CODES.get(key.lower())
        if not scan_code:
            print(f"未知按键: {key}")
            return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            return True
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False
    
    def send_combination(self, keys):
        """发送组合键"""
        scan_codes = []
        for key in keys:
            scan_code = HARDWARE_SCAN_CODES.get(key.lower())
            if scan_code:
                scan_codes.append(scan_code)
            else:
                print(f"未知按键: {key}")
                return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 按下所有键
            for scan_code in scan_codes:
                self.user32.keybd_event(0, scan_code, 0x0008, 0)
                time.sleep(0.01)
            
            time.sleep(0.05)
            
            # 释放所有键（逆序）
            for scan_code in reversed(scan_codes):
                self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
                time.sleep(0.01)
            
            return True
        except Exception as e:
            print(f"发送组合键失败: {e}")
            return False
    
    def guided_enter_dungeon(self):
        """引导式进入地下城"""
        print("\n=== 引导式进入地下城 ===")
        
        # 步骤1: 确认在城镇
        print("步骤1: 确认角色状态")
        print("请确保:")
        print("- 角色在城镇中")
        print("- 面向地图传送门")
        print("- 没有对话框打开")
        
        choice = input("确认角色状态正确? (y/n): ").strip().lower()
        if choice != 'y':
            print("请先调整角色状态")
            return False
        
        # 步骤2: 进入地图选择
        print("\n步骤2: 进入地图选择")
        print("我将按右键进入地图选择...")
        input("按回车继续...")
        
        self.send_key('right')
        time.sleep(2)
        
        choice = input("是否成功进入地图选择界面? (y/n): ").strip().lower()
        if choice != 'y':
            print("进入地图选择失败，请检查角色位置")
            return False
        
        # 步骤3: 选择格兰迪
        print("\n步骤3: 选择格兰迪")
        print("请手动选择格兰迪地下城")
        print("选择地狱难度")
        if self.enable_abyss:
            print("我将按F11开启深渊模式...")
            self.send_key('f11')
            time.sleep(0.5)
        
        input("选择完成后按回车...")
        
        # 步骤4: 确认进入
        print("\n步骤4: 确认进入")
        print("我将按空格确认进入...")
        input("按回车继续...")
        
        self.send_key('space')
        time.sleep(4)
        
        choice = input("是否成功进入地下城? (y/n): ").strip().lower()
        if choice != 'y':
            print("进入地下城失败")
            return False
        
        print("✓ 成功进入地下城!")
        return True
    
    def guided_clear_dungeon(self):
        """引导式清理地下城"""
        print("\n=== 引导式清理地下城 ===")
        
        for room in range(8):
            print(f"\n房间 {room + 1}/8")
            
            # 移动
            print("1. 向右移动到房间中央")
            for i in range(4):
                print(f"  移动 {i+1}/4")
                self.send_key('right')
                time.sleep(0.2)
            
            # 攻击
            print(f"2. 释放技能 {self.skill_key}")
            for i in range(8):
                print(f"  技能 {i+1}/8")
                self.send_key(self.skill_key)
                time.sleep(0.15)
            
            # 等待
            print("3. 等待怪物死亡...")
            time.sleep(2)
            
            # 检查是否清空
            choice = input("房间是否清空? (y/n): ").strip().lower()
            if choice != 'y':
                print("继续攻击...")
                for i in range(5):
                    self.send_key(self.skill_key)
                    time.sleep(0.2)
                time.sleep(1)
            
            # 顺图
            if room < 7:
                print("4. 顺图到下一房间")
                print("我将按Alt+Right顺图...")
                
                self.send_combination(['alt', 'right'])
                time.sleep(2)
                
                choice = input("是否成功进入下一房间? (y/n): ").strip().lower()
                if choice != 'y':
                    print("顺图失败，请手动调整")
                    input("调整完成后按回车继续...")
        
        print("✓ 地下城清理完成!")
        return True
    
    def guided_exit_dungeon(self):
        """引导式退出地下城"""
        print("\n=== 引导式退出地下城 ===")
        
        print("等待翻牌或结算界面...")
        time.sleep(3)
        
        print("按ESC退出...")
        for i in range(4):
            print(f"ESC {i+1}/4")
            self.send_key('esc')
            time.sleep(1.5)
        
        choice = input("是否成功返回城镇? (y/n): ").strip().lower()
        if choice != 'y':
            print("继续按ESC...")
            for i in range(2):
                self.send_key('esc')
                time.sleep(1)
        
        print("按数字键4...")
        self.send_key('4')
        time.sleep(2)
        
        print("✓ 退出地下城完成!")
        return True
    
    def guided_handle_inventory(self):
        """引导式处理背包"""
        print("\n=== 引导式处理背包 ===")
        
        if self.use_stall_decompose:
            print("使用摆摊分解...")
            print("按0键打开摆摊分解机...")
            self.send_key('0')
            time.sleep(3)
            
            print("按Alt+D分解装备...")
            self.send_combination(['alt', 'd'])
            time.sleep(2)
            
            print("按Alt+W修理装备...")
            self.send_combination(['alt', 'w'])
            time.sleep(1.5)
        else:
            print("直接分解装备...")
            self.send_combination(['alt', 'd'])
            time.sleep(1.5)
        
        print("✓ 背包处理完成!")
        return True
    
    def guided_farming_cycle(self):
        """引导式完整搬砖循环"""
        print("\n" + "=" * 50)
        print("引导式搬砖循环")
        print("=" * 50)
        
        # 1. 进入地下城
        if not self.guided_enter_dungeon():
            return False
        
        # 2. 清理地下城
        if not self.guided_clear_dungeon():
            return False
        
        # 3. 退出地下城
        if not self.guided_exit_dungeon():
            return False
        
        # 4. 处理背包
        if not self.guided_handle_inventory():
            return False
        
        print("\n" + "=" * 50)
        print("✓ 完整搬砖循环完成!")
        print("=" * 50)
        return True
    
    def auto_farming_loop(self):
        """自动搬砖循环"""
        print("\n开始自动搬砖循环...")
        print("每轮完成后会询问是否继续")
        
        cycle = 0
        while True:
            cycle += 1
            print(f"\n第 {cycle} 轮搬砖")
            
            if self.guided_farming_cycle():
                choice = input("\n是否继续下一轮? (y/n): ").strip().lower()
                if choice != 'y':
                    break
            else:
                print("本轮搬砖失败")
                choice = input("是否重试? (y/n): ").strip().lower()
                if choice != 'y':
                    break
        
        print("自动搬砖结束")

def main():
    """主函数"""
    print("DNF引导式搬砖机器人")
    print("=" * 40)
    print("通过用户确认来解决状态判断问题")
    print()
    
    bot = DNFGuidedBot()
    
    if not bot.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    print(f"当前配置:")
    print(f"- 技能键: {bot.skill_key}")
    print(f"- 深渊模式: {'开启' if bot.enable_abyss else '关闭'}")
    print(f"- 摆摊分解: {'开启' if bot.use_stall_decompose else '关闭'}")
    
    while True:
        print("\n请选择操作:")
        print("1. 单次引导搬砖")
        print("2. 自动搬砖循环")
        print("3. 测试按键")
        print("0. 退出")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            bot.guided_farming_cycle()
        elif choice == '2':
            bot.auto_farming_loop()
        elif choice == '3':
            key = input("请输入要测试的按键: ").strip()
            if key:
                bot.send_key(key)
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
