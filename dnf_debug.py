#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF调试工具
用于诊断为什么角色不移动
"""

import ctypes
import time
import os
from ctypes import wintypes, byref

# 扫描码映射
SCAN_CODES = {
    'right': 0x4D, 'left': 0x4B, 'up': 0x48, 'down': 0x50,
    'e': 0x12, 'space': 0x39, 'esc': 0x01, '4': 0x05,
    'alt': 0x38, '0': 0x0B
}

class DNFDebugger:
    """DNF调试器"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.game_hwnd = None
        self.game_pid = None
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        pid = wintypes.DWORD()
                        self.user32.GetWindowThreadProcessId(hwnd, byref(pid))
                        self.game_hwnd = hwnd
                        self.game_pid = pid.value
                        print(f"找到DNF窗口: {window_title} (句柄: {hwnd}, PID: {self.game_pid})")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        return self.game_hwnd is not None
    
    def get_window_info(self):
        """获取窗口详细信息"""
        if not self.game_hwnd:
            return
        
        # 获取窗口位置和大小
        rect = wintypes.RECT()
        self.user32.GetWindowRect(self.game_hwnd, byref(rect))
        
        # 获取客户区大小
        client_rect = wintypes.RECT()
        self.user32.GetClientRect(self.game_hwnd, byref(client_rect))
        
        # 检查窗口状态
        is_foreground = self.user32.GetForegroundWindow() == self.game_hwnd
        is_active = self.user32.GetActiveWindow() == self.game_hwnd
        is_focus = self.user32.GetFocus() == self.game_hwnd
        
        print(f"\n窗口信息:")
        print(f"  窗口位置: ({rect.left}, {rect.top}) - ({rect.right}, {rect.bottom})")
        print(f"  窗口大小: {rect.right - rect.left} x {rect.bottom - rect.top}")
        print(f"  客户区大小: {client_rect.right} x {client_rect.bottom}")
        print(f"  是否前台窗口: {is_foreground}")
        print(f"  是否活动窗口: {is_active}")
        print(f"  是否有焦点: {is_focus}")
    
    def check_game_state(self):
        """检查游戏状态"""
        print(f"\n游戏状态检查:")
        print(f"请确认以下状态:")
        print(f"1. 角色是否在城镇中？")
        print(f"2. 是否有对话框打开？")
        print(f"3. 是否在聊天输入状态？")
        print(f"4. 角色是否可以自由移动？")
        print(f"5. 是否有其他界面覆盖？")
    
    def test_activation(self):
        """测试窗口激活"""
        print(f"\n测试窗口激活...")
        
        if not self.game_hwnd:
            print("错误: 未找到游戏窗口")
            return False
        
        try:
            # 多种方式激活窗口
            print("1. ShowWindow...")
            self.user32.ShowWindow(self.game_hwnd, 9)  # SW_RESTORE
            time.sleep(0.2)
            
            print("2. SetForegroundWindow...")
            result1 = self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.2)
            
            print("3. BringWindowToTop...")
            result2 = self.user32.BringWindowToTop(self.game_hwnd)
            time.sleep(0.2)
            
            print("4. SetActiveWindow...")
            result3 = self.user32.SetActiveWindow(self.game_hwnd)
            time.sleep(0.2)
            
            print(f"激活结果: SetForegroundWindow={result1}, BringWindowToTop={result2}, SetActiveWindow={result3}")
            
            # 检查是否成功激活
            current_foreground = self.user32.GetForegroundWindow()
            success = current_foreground == self.game_hwnd
            print(f"激活成功: {success}")
            
            return success
            
        except Exception as e:
            print(f"窗口激活失败: {e}")
            return False
    
    def test_key_methods(self, key='right'):
        """测试不同的按键方法"""
        scan_code = SCAN_CODES.get(key)
        if not scan_code:
            print(f"未知按键: {key}")
            return
        
        print(f"\n测试按键方法 - {key} (扫描码: 0x{scan_code:02X})")
        
        # 激活窗口
        self.test_activation()
        time.sleep(0.5)
        
        methods = [
            ("方法1: keybd_event + 扫描码", self.method1_keybd_event),
            ("方法2: SendInput + 扫描码", self.method2_sendinput),
            ("方法3: PostMessage", self.method3_postmessage),
            ("方法4: SendMessage", self.method4_sendmessage),
        ]
        
        for name, method in methods:
            print(f"\n{name}")
            try:
                result = method(scan_code)
                print(f"  结果: {'成功' if result else '失败'}")
                print(f"  请观察角色是否移动...")
                time.sleep(3)  # 给时间观察
            except Exception as e:
                print(f"  异常: {e}")
    
    def method1_keybd_event(self, scan_code):
        """方法1: keybd_event + 扫描码"""
        self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
        time.sleep(0.05)
        self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
        return True
    
    def method2_sendinput(self, scan_code):
        """方法2: SendInput + 扫描码"""
        from ctypes import Structure, Union, POINTER
        
        class KEYBDINPUT(Structure):
            _fields_ = [("wVk", wintypes.WORD), ("wScan", wintypes.WORD),
                       ("dwFlags", wintypes.DWORD), ("time", wintypes.DWORD),
                       ("dwExtraInfo", POINTER(wintypes.ULONG))]
        
        class INPUT_UNION(Union):
            _fields_ = [("ki", KEYBDINPUT)]
        
        class INPUT(Structure):
            _fields_ = [("type", wintypes.DWORD), ("ii", INPUT_UNION)]
        
        inputs = (INPUT * 2)()
        
        # 按下
        inputs[0].type = 1
        inputs[0].ii.ki.wVk = 0
        inputs[0].ii.ki.wScan = scan_code
        inputs[0].ii.ki.dwFlags = 0x0008
        inputs[0].ii.ki.time = 0
        inputs[0].ii.ki.dwExtraInfo = None
        
        # 释放
        inputs[1].type = 1
        inputs[1].ii.ki.wVk = 0
        inputs[1].ii.ki.wScan = scan_code
        inputs[1].ii.ki.dwFlags = 0x0008 | 0x0002
        inputs[1].ii.ki.time = 0
        inputs[1].ii.ki.dwExtraInfo = None
        
        result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
        return result == 2
    
    def method3_postmessage(self, scan_code):
        """方法3: PostMessage"""
        WM_KEYDOWN = 0x0100
        WM_KEYUP = 0x0101
        
        # 转换为虚拟键码
        vk_code = self.user32.MapVirtualKeyW(scan_code, 1)  # MAPVK_VSC_TO_VK
        
        lParam_down = (scan_code << 16) | 1
        lParam_up = (scan_code << 16) | 0xC0000001
        
        result1 = self.user32.PostMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)
        time.sleep(0.05)
        result2 = self.user32.PostMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)
        
        return result1 and result2
    
    def method4_sendmessage(self, scan_code):
        """方法4: SendMessage"""
        WM_KEYDOWN = 0x0100
        WM_KEYUP = 0x0101
        
        vk_code = self.user32.MapVirtualKeyW(scan_code, 1)
        
        lParam_down = (scan_code << 16) | 1
        lParam_up = (scan_code << 16) | 0xC0000001
        
        self.user32.SendMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)
        time.sleep(0.05)
        self.user32.SendMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)
        
        return True
    
    def interactive_test(self):
        """交互式测试"""
        while True:
            print(f"\n=== DNF调试菜单 ===")
            print(f"1. 检查窗口信息")
            print(f"2. 检查游戏状态")
            print(f"3. 测试窗口激活")
            print(f"4. 测试右方向键")
            print(f"5. 测试技能键E")
            print(f"6. 测试空格键")
            print(f"7. 测试ESC键")
            print(f"8. 自定义按键测试")
            print(f"9. 连续移动测试")
            print(f"0. 退出")
            
            choice = input(f"\n请选择: ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                self.get_window_info()
            elif choice == '2':
                self.check_game_state()
            elif choice == '3':
                self.test_activation()
            elif choice == '4':
                self.test_key_methods('right')
            elif choice == '5':
                self.test_key_methods('e')
            elif choice == '6':
                self.test_key_methods('space')
            elif choice == '7':
                self.test_key_methods('esc')
            elif choice == '8':
                key = input("请输入要测试的按键: ").strip()
                if key and key in SCAN_CODES:
                    self.test_key_methods(key)
                else:
                    print("无效按键")
            elif choice == '9':
                print("连续移动测试 - 观察角色是否移动")
                for i in range(5):
                    print(f"移动 {i+1}/5")
                    self.method1_keybd_event(SCAN_CODES['right'])
                    time.sleep(0.5)
            else:
                print("无效选择")

def main():
    """主函数"""
    print("DNF调试工具")
    print("=" * 30)
    print("用于诊断角色不移动的问题")
    print()
    
    debugger = DNFDebugger()
    
    if not debugger.find_dnf_window():
        print("错误: 未找到DNF窗口")
        print("请确保DNF游戏已启动")
        return
    
    print("\n请确保:")
    print("1. DNF角色在可以移动的地方（城镇或地下城）")
    print("2. 没有对话框或菜单打开")
    print("3. 不在聊天输入状态")
    print("4. 角色没有被控制限制")
    
    debugger.interactive_test()

if __name__ == "__main__":
    main()
