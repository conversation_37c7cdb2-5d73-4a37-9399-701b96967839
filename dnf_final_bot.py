#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF最终版搬砖机器人
集成简单有效的门识别系统
"""

import ctypes
import time
import os
import cv2
import numpy as np
from PIL import ImageGrab
import configparser
import logging
from datetime import datetime
from ctypes import wintypes, byref, Structure

class DNFFinalBot:
    """DNF最终版搬砖机器人"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.game_hwnd = None
        self.window_rect = None
        
        # 配置参数
        self.skill_key = 'e'
        self.work_hours = 8
        self.use_stall_decompose = True
        self.enable_abyss = True  # 默认开启深渊模式
        self.decompose_interval = 5  # 每5次地下城后分解修理
        
        # 界面模板
        self.templates = {}

        # 屏幕键盘坐标（需要根据实际位置调整）
        self.screen_keyboard = {
            'alt': (100, 100),      # Alt键的屏幕坐标
            'right': (200, 100),    # 右方向键的屏幕坐标
            'up': (200, 80),        # 上方向键的屏幕坐标
            'down': (200, 120),     # 下方向键的屏幕坐标
            'left': (180, 100),     # 左方向键的屏幕坐标
            'd': (150, 100),        # D键的屏幕坐标
            'w': (130, 100)         # W键的屏幕坐标
        }

        # 运行状态
        self.running = False
        self.start_time = None

        # 初始化
        self.setup_logging()
        self.load_config()
        self.enable_debug_privilege()
        self.load_templates()
    
    def setup_logging(self):
        """设置日志"""
        os.makedirs("logs", exist_ok=True)
        log_file = f"logs/dnf_final_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            self.work_hours = config.getint('GENERAL', 'work_hours', fallback=8)
            self.use_stall_decompose = config.getboolean('FEATURES', 'use_stall_decompose', fallback=True)
            self.enable_abyss = config.getboolean('FEATURES', 'enable_abyss', fallback=True)
            self.decompose_interval = config.getint('FEATURES', 'decompose_interval', fallback=5)
            
            self.logger.info(f"配置加载完成 - 技能键: {self.skill_key}, 工作时长: {self.work_hours}小时")
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                self.logger.info("调试权限已启用")
        except Exception as e:
            self.logger.warning(f"调试权限启用失败: {e}")
    


    def load_templates(self):
        """加载界面模板"""
        template_dir = "templates"
        if not os.path.exists(template_dir):
            self.logger.warning("模板目录不存在: templates")
            return

        template_files = [f for f in os.listdir(template_dir) if f.endswith('.png')]

        for template_file in template_files:
            template_name = template_file[:-4]  # 去掉.png后缀
            template_path = os.path.join(template_dir, template_file)
            template = cv2.imread(template_path)

            if template is not None:
                self.templates[template_name] = template
                self.logger.debug(f"加载模板: {template_name}")

        self.logger.info(f"共加载 {len(self.templates)} 个界面模板")

    def find_template(self, template_name, threshold=0.7):
        """查找模板"""
        if template_name not in self.templates:
            self.logger.warning(f"模板不存在: {template_name}")
            return None

        screen = self.capture_screen()
        if screen is None:
            return None

        template = self.templates[template_name]
        result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

        if max_val > threshold:
            h, w = template.shape[:2]
            center_x = max_loc[0] + w // 2
            center_y = max_loc[1] + h // 2
            self.logger.debug(f"找到模板 {template_name}: 匹配度{max_val:.3f}")
            return (center_x, center_y, max_val)

        self.logger.debug(f"未找到模板 {template_name}: 最高匹配度{max_val:.3f}")
        return None

    def click_template(self, template_name, threshold=0.7):
        """点击模板位置"""
        result = self.find_template(template_name, threshold)
        if result:
            x, y, confidence = result
            self.logger.info(f"点击 {template_name}: 位置({x}, {y})")

            # 转换为屏幕坐标
            if self.window_rect:
                screen_x = self.window_rect[0] + x
                screen_y = self.window_rect[1] + y

                # 鼠标点击
                self.user32.SetCursorPos(screen_x, screen_y)
                time.sleep(0.1)
                self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # 左键按下
                time.sleep(0.05)
                self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # 左键释放

                return True
        else:
            self.logger.warning(f"未找到可点击的 {template_name}")
            return False
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        self.logger.info(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def capture_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            screen = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            return screen
        except Exception as e:
            self.logger.error(f"截屏失败: {e}")
            return None
    
    def send_key(self, key):
        """发送按键"""
        scan_codes = {
            'e': 0x12, 'space': 0x39, 'esc': 0x01, '4': 0x05, '0': 0x0B,
            'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0,
            'alt': 0x38, 'f11': 0x57, 'i': 0x17, 'd': 0x20, 'w': 0x11, 'q': 0x10
        }
        
        scan_code = scan_codes.get(key.lower())
        if not scan_code:
            self.logger.error(f"未知按键: {key}")
            return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            return True
        except Exception as e:
            self.logger.error(f"发送按键失败: {e}")
            return False
    
    def send_combination(self, keys):
        """发送组合键"""
        scan_codes = {
            'alt': 0x38, 'q': 0x10, 'w': 0x11, 'd': 0x20,
            'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0
        }
        
        codes = []
        for key in keys:
            code = scan_codes.get(key.lower())
            if code:
                codes.append(code)
            else:
                self.logger.error(f"未知组合键: {key}")
                return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 按下所有键
            for code in codes:
                self.user32.keybd_event(0, code, 0x0008, 0)
                time.sleep(0.01)
            
            time.sleep(0.1)
            
            # 释放所有键（逆序）
            for code in reversed(codes):
                self.user32.keybd_event(0, code, 0x0008 | 0x0002, 0)
                time.sleep(0.01)
            
            return True
        except Exception as e:
            self.logger.error(f"发送组合键失败: {e}")
            return False

    def click_screen_keyboard(self, key):
        """点击屏幕键盘"""
        if key not in self.screen_keyboard:
            self.logger.error(f"未知的屏幕键盘按键: {key}")
            return False

        x, y = self.screen_keyboard[key]
        self.logger.debug(f"点击屏幕键盘 {key}: 位置({x}, {y})")

        try:
            # 移动鼠标到指定位置
            self.user32.SetCursorPos(x, y)
            time.sleep(0.1)

            # 鼠标左键点击
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # 左键按下
            time.sleep(0.05)
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # 左键释放

            self.logger.debug(f"已点击屏幕键盘 {key}")
            return True
        except Exception as e:
            self.logger.error(f"点击屏幕键盘失败: {e}")
            return False

    def screen_keyboard_alt_right(self):
        """屏幕键盘Alt+Right顺图"""
        self.logger.info("使用屏幕键盘进行顺图: Alt+Right")

        try:
            # 1. 按住Alt键
            alt_x, alt_y = self.screen_keyboard['alt']
            self.user32.SetCursorPos(alt_x, alt_y)
            time.sleep(0.1)
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # Alt按下
            self.logger.debug("按住Alt键")

            # 2. 等待一下
            time.sleep(0.2)

            # 3. 点击Right键
            right_x, right_y = self.screen_keyboard['right']
            self.user32.SetCursorPos(right_x, right_y)
            time.sleep(0.1)
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # Right按下
            time.sleep(0.05)
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # Right释放
            self.logger.debug("点击Right键")

            # 4. 释放Alt键
            self.user32.SetCursorPos(alt_x, alt_y)
            time.sleep(0.1)
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # Alt释放
            self.logger.debug("释放Alt键")

            self.logger.info("屏幕键盘Alt+Right完成")
            return True

        except Exception as e:
            self.logger.error(f"屏幕键盘Alt+Right失败: {e}")
            return False

    def screen_keyboard_alt_d(self):
        """屏幕键盘Alt+D分解"""
        self.logger.info("使用屏幕键盘进行分解: Alt+D")

        try:
            # 1. 按住Alt键
            alt_x, alt_y = self.screen_keyboard['alt']
            self.user32.SetCursorPos(alt_x, alt_y)
            time.sleep(0.1)
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # Alt按下
            self.logger.debug("按住Alt键")

            # 2. 等待一下
            time.sleep(0.2)

            # 3. 点击D键
            if 'd' not in self.screen_keyboard:
                self.logger.error("屏幕键盘未设置D键坐标")
                return False

            d_x, d_y = self.screen_keyboard['d']
            self.user32.SetCursorPos(d_x, d_y)
            time.sleep(0.1)
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # D按下
            time.sleep(0.05)
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # D释放
            self.logger.debug("点击D键")

            # 4. 释放Alt键
            self.user32.SetCursorPos(alt_x, alt_y)
            time.sleep(0.1)
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # Alt释放
            self.logger.debug("释放Alt键")

            self.logger.info("屏幕键盘Alt+D完成")
            return True

        except Exception as e:
            self.logger.error(f"屏幕键盘Alt+D失败: {e}")
            return False

    def screen_keyboard_alt_w(self):
        """屏幕键盘Alt+W修理"""
        self.logger.info("使用屏幕键盘进行修理: Alt+W")

        try:
            # 1. 按住Alt键
            alt_x, alt_y = self.screen_keyboard['alt']
            self.user32.SetCursorPos(alt_x, alt_y)
            time.sleep(0.1)
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # Alt按下
            self.logger.debug("按住Alt键")

            # 2. 等待一下
            time.sleep(0.2)

            # 3. 点击W键
            if 'w' not in self.screen_keyboard:
                self.logger.error("屏幕键盘未设置W键坐标")
                return False

            w_x, w_y = self.screen_keyboard['w']
            self.user32.SetCursorPos(w_x, w_y)
            time.sleep(0.1)
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # W按下
            time.sleep(0.05)
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # W释放
            self.logger.debug("点击W键")

            # 4. 释放Alt键
            self.user32.SetCursorPos(alt_x, alt_y)
            time.sleep(0.1)
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # Alt释放
            self.logger.debug("释放Alt键")

            self.logger.info("屏幕键盘Alt+W完成")
            return True

        except Exception as e:
            self.logger.error(f"屏幕键盘Alt+W失败: {e}")
            return False

    def decompose_and_repair(self):
        """分解和修理装备"""
        self.logger.info("开始分解和修理装备")

        try:
            # 1. 按0键打开摆摊分解界面
            self.logger.info("按0键打开摆摊分解界面")
            self.send_key('0')
            time.sleep(2)

            # 2. 检测并点击摆摊分解按钮
            if self.find_template("stall_decompose"):
                self.logger.info("找到摆摊分解按钮，点击确认")
                self.click_template("stall_decompose")
                time.sleep(2)
            else:
                self.logger.warning("未找到摆摊分解按钮")
                return False

            # 3. 再按0键确认摆摊
            self.logger.info("再按0键确认摆摊")
            self.send_key('0')
            time.sleep(3)  # 等待摆摊界面出现

            # 4. 寻找绿色摆摊位置并点击
            self.logger.info("寻找绿色摆摊位置")
            # 这里需要实现绿色摆摊位置检测，暂时使用固定位置
            # TODO: 实现绿色摆摊位置检测
            stall_x, stall_y = 400, 300  # 临时固定坐标，需要根据实际调整
            self.user32.SetCursorPos(stall_x, stall_y)
            time.sleep(0.5)
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # 左键按下
            time.sleep(0.1)
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # 左键释放
            self.logger.info(f"点击摆摊位置: ({stall_x}, {stall_y})")
            time.sleep(2)

            # 5. 使用屏幕键盘Alt+D分解
            self.logger.info("使用Alt+D分解装备")
            if self.screen_keyboard_alt_d():
                time.sleep(3)  # 等待分解完成
                self.logger.info("分解完成")
            else:
                self.logger.error("分解失败")

            # 6. 使用屏幕键盘Alt+W修理
            self.logger.info("使用Alt+W修理装备")
            if self.screen_keyboard_alt_w():
                time.sleep(2)  # 等待修理完成
                self.logger.info("修理完成")
            else:
                self.logger.error("修理失败")

            # 7. 按ESC关闭摆摊
            self.logger.info("按ESC关闭摆摊")
            self.send_key('esc')
            time.sleep(1)

            # 8. 点击确认关闭摆摊
            # TODO: 实现确认按钮检测和点击
            confirm_x, confirm_y = 400, 350  # 临时固定坐标
            self.user32.SetCursorPos(confirm_x, confirm_y)
            time.sleep(0.5)
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # 左键按下
            time.sleep(0.1)
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # 左键释放
            self.logger.info("点击确认关闭摆摊")
            time.sleep(2)

            self.logger.info("分解和修理装备完成")
            return True

        except Exception as e:
            self.logger.error(f"分解和修理装备失败: {e}")
            return False

    def find_door(self):
        """寻找门（普通门或BOSS门）"""
        screen = self.capture_screen()
        if screen is None:
            return None
        
        doors = []
        
        # 寻找普通门
        if self.normal_door_template is not None:
            result = cv2.matchTemplate(screen, self.normal_door_template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val > 0.6:  # 降低阈值到0.6
                h, w = self.normal_door_template.shape[:2]
                door_x = max_loc[0] + w // 2
                door_y = max_loc[1] + h // 2
                doors.append((door_x, door_y, "普通门", max_val))
                self.logger.debug(f"找到普通门: 位置({door_x}, {door_y}), 匹配度{max_val:.3f}")
        
        # 寻找BOSS门
        if self.boss_door_template is not None:
            result = cv2.matchTemplate(screen, self.boss_door_template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val > 0.6:  # 降低阈值到0.6
                h, w = self.boss_door_template.shape[:2]
                door_x = max_loc[0] + w // 2
                door_y = max_loc[1] + h // 2
                doors.append((door_x, door_y, "BOSS门", max_val))
                self.logger.debug(f"找到BOSS门: 位置({door_x}, {door_y}), 匹配度{max_val:.3f}")
        
        # 返回匹配度最高的门
        if doors:
            best_door = max(doors, key=lambda x: x[3])
            self.logger.info(f"选择{best_door[2]}: 位置({best_door[0]}, {best_door[1]})")
            return best_door[:3]  # 返回 (x, y, type)
        
        return None
    
    def find_and_move_to_door(self):
        """寻找门并移动到门口"""
        self.logger.info("开始寻找门")
        
        # 最多尝试10次，每次向右移动寻找
        for attempt in range(10):
            door_info = self.find_door()
            
            if door_info:
                door_x, door_y, door_type = door_info
                self.logger.info(f"找到{door_type}: 位置({door_x}, {door_y})")
                
                # 移动到门口
                screen = self.capture_screen()
                if screen is None:
                    return False
                
                h, w = screen.shape[:2]
                char_x = w // 2  # 假设角色在画面中央
                
                # 计算移动距离
                if door_x > char_x + 50:  # 门在右边
                    move_steps = min(5, (door_x - char_x) // 80)
                    self.logger.info(f"向右移动 {move_steps} 步到达门口")
                    
                    for _ in range(move_steps):
                        self.send_key('right')
                        time.sleep(0.2)
                
                self.logger.info("已到达门口")
                return True
            else:
                # 当前画面没有门，继续向右寻找
                self.logger.debug(f"第{attempt+1}次未找到门，向右移动寻找")
                for _ in range(3):
                    self.send_key('right')
                    time.sleep(0.15)
        
        self.logger.warning("未找到门")
        return False

    def enter_grandi(self):
        """进入格兰迪地下城"""
        self.logger.info("开始进入格兰迪")

        # 1. 向右走进入地图选择
        self.logger.info("向右走进入地图选择")
        self.send_key('right')
        time.sleep(3)

        # 2. 等待地图选择界面出现
        if not self.wait_for_template("map_selection_interface", timeout=5):
            self.logger.error("未能进入地图选择界面")
            return False

        # 3. 选择格兰迪
        self.logger.info("选择格兰迪")
        if self.find_template("grandi_option"):
            # 如果找到格兰迪选项但没有选中，点击选择
            if not self.find_template("grandi_selected"):
                self.click_template("grandi_option")
                time.sleep(1)
        else:
            self.logger.warning("未找到格兰迪选项")

        # 4. 选择地狱难度
        self.logger.info("选择地狱难度")
        if self.find_template("hell_difficulty"):
            if not self.find_template("hell_selected"):
                self.click_template("hell_difficulty")
                time.sleep(1)
        else:
            self.logger.warning("未找到地狱难度选项")

        # 5. 开启深渊模式（如果启用）
        if self.enable_abyss:
            self.logger.info("在地图选择页面开启深渊模式")
            # 按F11开启深渊
            self.send_key('f11')
            time.sleep(1)  # 等待深渊状态更新
            self.logger.info("深渊模式已开启")

        # 6. 确认进入
        self.logger.info("确认进入格兰迪")
        self.send_key('space')
        time.sleep(5)  # 等待加载

        return False

    def click_template(self, template_name, threshold=0.7):
        """点击模板位置"""
        screen = self.capture_screen()
        if screen is None:
            return False

        if template_name not in self.templates:
            self.logger.error(f"模板 {template_name} 不存在")
            return False

        template = self.templates[template_name]
        result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, max_loc = cv2.minMaxLoc(result)

        if max_val >= threshold:
            # 计算点击位置（模板中心）
            h, w = template.shape[:2]
            center_x = max_loc[0] + w // 2
            center_y = max_loc[1] + h // 2

            # 转换为屏幕坐标
            screen_x = self.game_window_x + center_x
            screen_y = self.game_window_y + center_y

            # 点击
            self.user32.SetCursorPos(screen_x, screen_y)
            time.sleep(0.2)
            self.user32.mouse_event(0x0002, 0, 0, 0, 0)  # 左键按下
            time.sleep(0.1)
            self.user32.mouse_event(0x0004, 0, 0, 0, 0)  # 左键释放

            self.logger.info(f"点击模板 {template_name}: ({screen_x}, {screen_y})")
            return True

        return False

    def wait_for_template(self, template_name, timeout=10, threshold=0.7):
        """等待模板出现"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self.find_template(template_name, threshold):
                return True
            time.sleep(0.5)

        return False

    def clear_room(self, room_number):
        """清理房间"""
        self.logger.info(f"开始清理房间 {room_number}")

        # 第一个房间需要向前移动让地下怪物出现
        if room_number == 1:
            self.logger.info("第一个房间：向前移动让地下怪物出现")
            for _ in range(6):
                self.send_key('right')
                time.sleep(0.2)

            # 释放技能让地下怪物出现
            for _ in range(3):
                self.send_key(self.skill_key)
                time.sleep(0.5)

            # 继续向前走到怪物位置
            for _ in range(3):
                self.send_key('right')
                time.sleep(0.2)

        # 向右移动并释放技能
        for _ in range(8):
            self.send_key('right')
            time.sleep(0.1)

        # 释放技能清理怪物
        for _ in range(12):
            self.send_key(self.skill_key)
            time.sleep(0.2)

            # 边攻击边移动
            if _ % 3 == 0:
                self.send_key('right')
                time.sleep(0.1)

        # 等待怪物死亡和掉落
        time.sleep(2)

        # 检测房间清理完成标志
        self.logger.info("等待房间清理完成标志...")
        max_wait_time = 8  # 最大等待8秒
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            # 检测是否出现房间清理完成的标志（带?的小地图）
            if self.find_template("abyss_room_clear"):
                self.logger.info("✓ 检测到房间清理完成标志（小地图显示可前往房间）")
                self.logger.info(f"房间 {room_number} 清理完成")
                return True

            time.sleep(0.5)  # 每0.5秒检测一次

        # 超时后也认为清理完成
        self.logger.warning("未检测到房间清理完成标志，但已超时，继续执行")
        self.logger.info(f"房间 {room_number} 清理完成（超时）")
        return True

    def move_to_next_room(self):
        """移动到下一房间"""
        self.logger.info("准备移动到下一房间")

        # 向右移动到房间边缘
        self.logger.info("向右移动到房间边缘")
        for _ in range(8):
            self.send_key('right')
            time.sleep(0.1)

        # 使用屏幕键盘Alt+Right顺图
        if self.screen_keyboard_alt_right():
            # 等待进入下一房间
            time.sleep(2)
            self.logger.info("已进入下一房间")
            return True
        else:
            self.logger.warning("屏幕键盘顺图失败")
            return False

    def clear_dungeon(self):
        """清理整个地下城"""
        self.logger.info("开始清理地下城")

        for room in range(8):  # 格兰迪8个房间
            room_number = room + 1
            self.logger.info(f"=" * 30)
            self.logger.info(f"房间 {room_number}/8")

            # 清理当前房间
            self.clear_room(room_number)

            # 移动到下一房间（除了最后一个）
            if room < 7:
                if not self.move_to_next_room():
                    self.logger.error(f"无法移动到房间 {room_number + 1}")
                    return False

        self.logger.info("地下城清理完成")
        return True

    def exit_dungeon(self):
        """退出地下城"""
        self.logger.info("退出地下城")

        # 等待翻牌界面
        time.sleep(3)

        # 多次按ESC直到返回城镇
        for _ in range(5):
            self.send_key('esc')
            time.sleep(2)

        return True

    def handle_inventory(self):
        """处理背包"""
        self.logger.info("处理背包")

        if self.use_stall_decompose:
            # 摆摊分解
            self.logger.info("使用摆摊分解")
            self.send_key('0')  # 摆摊分解机
            time.sleep(3)

            # 分解装备
            self.send_combination(['alt', 'd'])
            time.sleep(2)

            # 修理装备
            self.send_combination(['alt', 'w'])
            time.sleep(1)

            # 按数字键4
            self.send_key('4')
            time.sleep(1)
        else:
            # 直接分解
            self.send_combination(['alt', 'd'])
            time.sleep(1)

    def farming_cycle(self):
        """完整的搬砖循环"""
        cycle_start = time.time()
        self.logger.info("=" * 50)
        self.logger.info("开始搬砖循环")

        try:
            # 1. 进入格兰迪
            if not self.enter_grandi():
                return False

            # 2. 清理地下城
            if not self.clear_dungeon():
                return False

            # 3. 退出地下城
            if not self.exit_dungeon():
                return False

            # 4. 处理背包
            self.handle_inventory()

            cycle_time = time.time() - cycle_start
            self.logger.info(f"搬砖循环完成，耗时: {cycle_time:.1f}秒")
            return True

        except Exception as e:
            self.logger.error(f"搬砖循环异常: {e}")
            return False

    def start_bot(self):
        """启动机器人"""
        if not self.find_dnf_window():
            self.logger.error("未找到DNF窗口")
            return

        # 检查屏幕键盘配置
        self.logger.info("使用屏幕键盘进行顺图")

        # 检查关键界面模板
        required_templates = ["map_selection_interface", "grandi_option"]
        missing_templates = []
        for template in required_templates:
            if template not in self.templates:
                missing_templates.append(template)

        if missing_templates:
            self.logger.warning(f"缺少关键模板: {missing_templates}")
            self.logger.warning("将使用基础功能，可能需要手动选择地图")

        self.logger.info("=" * 50)
        self.logger.info("DNF最终版搬砖机器人启动")
        self.logger.info(f"技能键: {self.skill_key}")
        self.logger.info(f"工作时长: {self.work_hours}小时")
        self.logger.info(f"摆摊分解: {'开启' if self.use_stall_decompose else '关闭'}")
        self.logger.info(f"深渊模式: {'开启' if self.enable_abyss else '关闭'}")
        self.logger.info(f"分解修理间隔: 每{self.decompose_interval}次地下城")
        self.logger.info(f"屏幕键盘顺图: ✓ 已启用")
        self.logger.info(f"界面模板: {len(self.templates)}个")
        self.logger.info("=" * 50)

        self.running = True
        self.start_time = time.time()
        cycle_count = 0
        success_count = 0

        try:
            while self.running:
                # 检查工作时长
                if time.time() - self.start_time >= (self.work_hours * 3600):
                    self.logger.info("已达到设定工作时长")
                    break

                cycle_count += 1
                self.logger.info(f"\n第 {cycle_count} 轮搬砖开始")

                # 执行搬砖循环
                if self.farming_cycle():
                    success_count += 1
                    self.logger.info(f"✓ 第 {cycle_count} 轮搬砖成功")

                    # 检查是否需要分解修理
                    if success_count % self.decompose_interval == 0:
                        self.logger.info(f"已完成 {success_count} 次地下城，开始分解修理")
                        if self.decompose_and_repair():
                            self.logger.info("分解修理完成，继续搬砖")
                        else:
                            self.logger.error("分解修理失败，继续搬砖")
                        time.sleep(3)
                else:
                    self.logger.warning(f"✗ 第 {cycle_count} 轮搬砖失败")

                # 等待下一轮
                time.sleep(5)

        except KeyboardInterrupt:
            self.logger.info("用户中断")
        finally:
            self.running = False
            total_time = (time.time() - self.start_time) / 3600
            self.logger.info(f"机器人停止，总运行时间: {total_time:.1f}小时")
            self.logger.info(f"总循环: {cycle_count} 次，成功: {success_count} 次")
            if cycle_count > 0:
                self.logger.info(f"成功率: {success_count/cycle_count*100:.1f}%")

def main():
    """主函数"""
    print("DNF最终版搬砖机器人")
    print("=" * 30)
    print("集成简单有效的门识别系统")
    print()

    bot = DNFFinalBot()

    while True:
        print("\n请选择操作:")
        print("1. 启动搬砖机器人")
        print("2. 测试屏幕键盘顺图")
        print("3. 测试房间清理完成检测")
        print("4. 测试分解修理功能")
        print("5. 测试地图选择和深渊")
        print("6. 单次搬砖测试")
        print("7. 设置屏幕键盘坐标")
        print("8. 查看配置")
        print("0. 退出")

        choice = input("\n请输入选择: ").strip()

        if choice == '0':
            break
        elif choice == '1':
            print("\n请确保:")
            print("- 角色在城镇中")
            print("- 面向地图传送门")
            print("- 已手动选择格兰迪和地狱难度")
            print("- 有 normal_door.png 和/或 boss_door.png 模板文件")
            input("确认后按回车开始...")
            bot.start_bot()
        elif choice == '2':
            if bot.find_dnf_window():
                print("\n测试屏幕键盘顺图功能")
                print("请确保:")
                print("1. 已开启屏幕键盘")
                print("2. 角色在地下城房间中")
                print("3. 准备测试Alt+Right顺图")

                input("准备好后按回车测试...")

                if bot.screen_keyboard_alt_right():
                    print("✓ 屏幕键盘顺图测试完成")
                else:
                    print("✗ 屏幕键盘顺图测试失败")
                    print("请检查屏幕键盘坐标设置")
            else:
                print("未找到DNF窗口")
        elif choice == '3':
            if bot.find_dnf_window():
                print("\n测试房间清理完成检测")
                print("请确保:")
                print("1. 角色在深渊地下城房间中")
                print("2. 房间已经清理完成")
                print("3. 小地图显示可前往的房间（带?标志）")

                input("准备好后按回车测试...")

                if bot.find_template("abyss_room_clear"):
                    print("✓ 成功检测到房间清理完成标志")
                    print("  小地图显示可前往房间，可以进行顺图")
                else:
                    print("✗ 未检测到房间清理完成标志")
                    print("  请确保房间已清理完成且小地图显示?标志")

                print("房间清理完成检测测试完成")
            else:
                print("未找到DNF窗口")

        elif choice == '4':
            if bot.find_dnf_window():
                print("\n测试分解修理功能")
                print("请确保:")
                print("1. 角色在城镇中")
                print("2. 已开启屏幕键盘")
                print("3. 背包中有装备需要分解")
                print("4. 已设置屏幕键盘D和W键坐标")

                input("准备好后按回车测试...")

                if bot.decompose_and_repair():
                    print("✓ 分解修理测试完成")
                else:
                    print("✗ 分解修理测试失败")
                    print("请检查屏幕键盘坐标和模板文件")
            else:
                print("未找到DNF窗口")

        elif choice == '5':
            if bot.find_dnf_window():
                print("\n测试地图选择和深渊功能")
                print("请确保角色在城镇面向传送门")
                input("准备好后按回车...")

                # 测试进入地图选择
                print("向右走进入地图选择...")
                bot.send_key('right')
                time.sleep(3)

                # 测试模板识别
                if bot.find_template("map_selection_interface"):
                    print("✓ 成功识别地图选择界面")
                else:
                    print("✗ 未识别到地图选择界面")

                if bot.find_template("grandi_option"):
                    print("✓ 找到格兰迪选项")
                else:
                    print("✗ 未找到格兰迪选项")

                # 测试F11深渊
                print("\n测试F11深渊功能...")
                print("现在在地图选择页面按F11测试深渊切换:")

                input("按回车测试F11...")
                bot.send_key('f11')
                time.sleep(1)
                print("已按F11，请观察深渊状态是否改变")

                choice = input("深渊状态是否正确切换? (y/n): ").strip().lower()
                if choice == 'y':
                    print("✓ F11深渊功能正常")
                else:
                    print("✗ F11深渊功能异常")

                print("F11测试完成")
            else:
                print("未找到DNF窗口")
        elif choice == '6':
            if bot.find_dnf_window():
                print("执行单次搬砖测试...")
                bot.farming_cycle()
            else:
                print("未找到DNF窗口")
        elif choice == '7':
            print("\n设置屏幕键盘坐标")
            print("请将鼠标移动到屏幕键盘的按键上，然后按回车记录坐标")

            keys_to_set = ['alt', 'right', 'up', 'down', 'left', 'd', 'w']

            for key in keys_to_set:
                input(f"\n请将鼠标移动到屏幕键盘的 {key.upper()} 键上，然后按回车...")

                # 获取当前鼠标位置
                point = wintypes.POINT()
                bot.user32.GetCursorPos(byref(point))
                x, y = point.x, point.y

                bot.screen_keyboard[key] = (x, y)
                print(f"{key.upper()} 键坐标已设置为: ({x}, {y})")

            print("\n屏幕键盘坐标设置完成!")
            print("当前坐标:")
            for key, (x, y) in bot.screen_keyboard.items():
                print(f"  {key.upper()}: ({x}, {y})")

        elif choice == '8':
            print(f"\n当前配置:")
            print(f"技能键: {bot.skill_key}")
            print(f"工作时长: {bot.work_hours}小时")
            print(f"摆摊分解: {'开启' if bot.use_stall_decompose else '关闭'}")
            print(f"深渊模式: {'开启' if bot.enable_abyss else '关闭'}")
            print(f"分解修理间隔: 每{bot.decompose_interval}次地下城")
            print(f"屏幕键盘顺图: ✓ 已启用")
            print(f"界面模板: {len(bot.templates)}个")

            print(f"\n屏幕键盘坐标:")
            for key, (x, y) in bot.screen_keyboard.items():
                print(f"  {key.upper()}: ({x}, {y})")

            if bot.templates:
                print("\n关键模板状态:")
                key_templates = ["map_selection_interface", "grandi_option", "hell_difficulty"]
                for template in key_templates:
                    status = "✓" if template in bot.templates else "✗"
                    print(f"  {status} {template}")

                print("\n可选模板:")
                optional_templates = ["grandi_selected", "hell_selected"]
                for template in optional_templates:
                    status = "✓" if template in bot.templates else "✗"
                    print(f"  {status} {template} (可选)")
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
