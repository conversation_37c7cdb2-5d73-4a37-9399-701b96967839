@echo off
title Install DNF Script Dependencies

echo ========================================
echo Installing DNF Bot Dependencies
echo ========================================
echo.

echo Using Tsinghua University mirror...
echo.

REM Upgrade pip
echo 1. Upgrading pip...
python -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple/

echo.
echo 2. Installing basic dependencies...

REM Install packages separately to avoid timeout
python -m pip install numpy==1.24.3 -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m pip install Pillow==10.0.1 -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m pip install opencv-python==******** -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m pip install pyautogui==0.9.54 -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m pip install keyboard==0.13.5 -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m pip install mouse==0.7.1 -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m pip install pynput==1.7.6 -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m pip install configparser==6.0.0 -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m pip install psutil==5.9.5 -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m pip install pywin32==306 -i https://pypi.tuna.tsinghua.edu.cn/simple/

echo.
echo ========================================
echo Dependencies installation completed!
echo ========================================
echo.

echo Now you can run:
echo python quick_test.py
echo.

pause
