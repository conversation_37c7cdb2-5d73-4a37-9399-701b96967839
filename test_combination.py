#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试组合键功能
专门测试Alt+方向键是否有效
"""

import ctypes
import time
from ctypes import wintypes, byref

# 扫描码
HARDWARE_SCAN_CODES = {
    'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0,
    'alt': 0x38, 'e': 0x12,
}

class CombinationTester:
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.enable_debug_privilege()
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = ctypes.windll.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                print("✓ 调试权限已启用")
        except Exception as e:
            print(f"调试权限启用失败: {e}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def send_combination_method1(self, keys):
        """方法1: 标准组合键（当前使用的方法）"""
        print(f"方法1: 标准组合键 {'+'.join(keys)}")
        
        scan_codes = []
        for key in keys:
            scan_code = HARDWARE_SCAN_CODES.get(key.lower())
            if scan_code:
                scan_codes.append(scan_code)
            else:
                print(f"未知按键: {key}")
                return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            print(f"  按下所有键...")
            # 按下所有键
            for i, scan_code in enumerate(scan_codes):
                print(f"    按下 {keys[i]} (0x{scan_code:02X})")
                self.user32.keybd_event(0, scan_code, 0x0008, 0)
                time.sleep(0.01)
            
            time.sleep(0.1)  # 保持按下状态
            
            print(f"  释放所有键...")
            # 释放所有键（逆序）
            for i, scan_code in enumerate(reversed(scan_codes)):
                print(f"    释放 {keys[len(keys)-1-i]} (0x{scan_code:02X})")
                self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
                time.sleep(0.01)
            
            print("  ✓ 组合键发送完成")
            return True
        except Exception as e:
            print(f"  ✗ 组合键发送失败: {e}")
            return False
    
    def send_combination_method2(self, keys):
        """方法2: 更长的按住时间"""
        print(f"方法2: 长按组合键 {'+'.join(keys)}")
        
        scan_codes = []
        for key in keys:
            scan_code = HARDWARE_SCAN_CODES.get(key.lower())
            if scan_code:
                scan_codes.append(scan_code)
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 按下所有键
            for scan_code in scan_codes:
                self.user32.keybd_event(0, scan_code, 0x0008, 0)
                time.sleep(0.02)
            
            time.sleep(0.3)  # 更长的按住时间
            
            # 释放所有键（逆序）
            for scan_code in reversed(scan_codes):
                self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
                time.sleep(0.02)
            
            print("  ✓ 长按组合键发送完成")
            return True
        except Exception as e:
            print(f"  ✗ 长按组合键发送失败: {e}")
            return False
    
    def send_combination_method3(self, keys):
        """方法3: 分步按键"""
        print(f"方法3: 分步按键 {'+'.join(keys)}")
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 先按下Alt
            alt_code = HARDWARE_SCAN_CODES['alt']
            print("  按下Alt...")
            self.user32.keybd_event(0, alt_code, 0x0008, 0)
            time.sleep(0.05)
            
            # 再按下方向键
            for key in keys:
                if key.lower() != 'alt':
                    direction_code = HARDWARE_SCAN_CODES.get(key.lower())
                    if direction_code:
                        print(f"  按下{key}...")
                        self.user32.keybd_event(0, direction_code, 0x0008, 0)
                        time.sleep(0.05)
                        
                        print(f"  释放{key}...")
                        self.user32.keybd_event(0, direction_code, 0x0008 | 0x0002, 0)
                        time.sleep(0.05)
            
            # 最后释放Alt
            print("  释放Alt...")
            self.user32.keybd_event(0, alt_code, 0x0008 | 0x0002, 0)
            
            print("  ✓ 分步按键发送完成")
            return True
        except Exception as e:
            print(f"  ✗ 分步按键发送失败: {e}")
            return False
    
    def test_all_methods(self, direction='right'):
        """测试所有组合键方法"""
        print(f"\n=== 测试 Alt+{direction} 组合键 ===")
        print("请确保角色在地下城中，观察是否顺图")
        
        methods = [
            ("方法1: 标准组合键", self.send_combination_method1),
            ("方法2: 长按组合键", self.send_combination_method2),
            ("方法3: 分步按键", self.send_combination_method3),
        ]
        
        for name, method in methods:
            print(f"\n{name}")
            try:
                result = method(['alt', direction])
                print(f"发送结果: {'成功' if result else '失败'}")
                
                response = input("是否成功顺图? (y/n): ").strip().lower()
                if response == 'y':
                    print(f"✓ {name} 有效!")
                    return name, method
                else:
                    print(f"✗ {name} 无效")
                    
                time.sleep(2)  # 等待观察
            except Exception as e:
                print(f"异常: {e}")
        
        print("\n所有方法都无效")
        return None, None
    
    def interactive_test(self):
        """交互式测试"""
        while True:
            print("\n" + "=" * 40)
            print("组合键测试工具")
            print("=" * 40)
            print("1. 测试 Alt+Right")
            print("2. 测试 Alt+Down")
            print("3. 测试 Alt+Up")
            print("4. 测试 Alt+Left")
            print("5. 测试所有方向")
            print("6. 自定义测试")
            print("0. 退出")
            
            choice = input("\n请选择: ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                self.test_all_methods('right')
            elif choice == '2':
                self.test_all_methods('down')
            elif choice == '3':
                self.test_all_methods('up')
            elif choice == '4':
                self.test_all_methods('left')
            elif choice == '5':
                for direction in ['right', 'down', 'up', 'left']:
                    print(f"\n测试 Alt+{direction}")
                    self.test_all_methods(direction)
                    input("按回车继续下一个方向...")
            elif choice == '6':
                direction = input("请输入方向 (right/down/up/left): ").strip()
                if direction in ['right', 'down', 'up', 'left']:
                    self.test_all_methods(direction)
                else:
                    print("无效方向")
            else:
                print("无效选择")

def main():
    """主函数"""
    print("DNF组合键测试工具")
    print("=" * 30)
    print("专门测试Alt+方向键顺图功能")
    
    tester = CombinationTester()
    
    if not tester.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    print("\n请确保:")
    print("1. 角色在地下城中")
    print("2. 当前房间已清空")
    print("3. 可以看到下个房间的门")
    
    tester.interactive_test()

if __name__ == "__main__":
    main()
