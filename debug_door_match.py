#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试门匹配 - 显示详细的匹配信息
"""

import ctypes
import time
import os
import cv2
import numpy as np
from PIL import ImageGrab
from ctypes import wintypes, byref

class DoorMatchDebugger:
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.window_rect = None
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        print(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def capture_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            screen = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            return screen
        except Exception as e:
            print(f"截屏失败: {e}")
            return None
    
    def debug_door_matching(self):
        """调试门匹配"""
        print("=== 门匹配调试 ===")
        
        # 检查模板文件
        normal_door_exists = os.path.exists("normal_door.png")
        boss_door_exists = os.path.exists("boss_door.png")
        
        print(f"normal_door.png 存在: {normal_door_exists}")
        print(f"boss_door.png 存在: {boss_door_exists}")
        
        if not normal_door_exists and not boss_door_exists:
            print("错误: 没有找到任何门模板文件")
            return
        
        # 截取当前画面
        screen = self.capture_screen()
        if screen is None:
            print("错误: 无法截取画面")
            return
        
        print(f"画面尺寸: {screen.shape[1]} x {screen.shape[0]}")
        
        # 保存当前画面
        cv2.imwrite("debug_current_screen.png", screen)
        print("已保存当前画面: debug_current_screen.png")
        
        # 测试普通门匹配
        if normal_door_exists:
            print("\n--- 测试普通门匹配 ---")
            normal_template = cv2.imread("normal_door.png")
            if normal_template is not None:
                print(f"普通门模板尺寸: {normal_template.shape[1]} x {normal_template.shape[0]}")
                
                # 模板匹配
                result = cv2.matchTemplate(screen, normal_template, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                
                print(f"普通门最高匹配度: {max_val:.4f}")
                print(f"最佳匹配位置: {max_loc}")
                
                # 在画面上标记最佳匹配位置
                h, w = normal_template.shape[:2]
                top_left = max_loc
                bottom_right = (top_left[0] + w, top_left[1] + h)
                
                screen_marked = screen.copy()
                cv2.rectangle(screen_marked, top_left, bottom_right, (0, 255, 0), 2)
                cv2.putText(screen_marked, f"Normal: {max_val:.3f}", 
                           (top_left[0], top_left[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                
                # 保存匹配结果图
                cv2.imwrite("debug_normal_match.png", screen_marked)
                print("已保存普通门匹配结果: debug_normal_match.png")
                
                # 保存匹配热力图
                result_normalized = cv2.normalize(result, None, 0, 255, cv2.NORM_MINMAX, cv2.CV_8U)
                result_colored = cv2.applyColorMap(result_normalized, cv2.COLORMAP_JET)
                cv2.imwrite("debug_normal_heatmap.png", result_colored)
                print("已保存普通门匹配热力图: debug_normal_heatmap.png")
                
                if max_val > 0.7:
                    print("✓ 普通门匹配成功 (阈值 > 0.7)")
                elif max_val > 0.5:
                    print("⚠ 普通门匹配度中等 (0.5-0.7)")
                else:
                    print("✗ 普通门匹配度低 (< 0.5)")
            else:
                print("错误: 无法加载普通门模板")
        
        # 测试BOSS门匹配
        if boss_door_exists:
            print("\n--- 测试BOSS门匹配 ---")
            boss_template = cv2.imread("boss_door.png")
            if boss_template is not None:
                print(f"BOSS门模板尺寸: {boss_template.shape[1]} x {boss_template.shape[0]}")
                
                # 模板匹配
                result = cv2.matchTemplate(screen, boss_template, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                
                print(f"BOSS门最高匹配度: {max_val:.4f}")
                print(f"最佳匹配位置: {max_loc}")
                
                # 在画面上标记最佳匹配位置
                h, w = boss_template.shape[:2]
                top_left = max_loc
                bottom_right = (top_left[0] + w, top_left[1] + h)
                
                screen_marked = screen.copy()
                cv2.rectangle(screen_marked, top_left, bottom_right, (0, 0, 255), 2)
                cv2.putText(screen_marked, f"Boss: {max_val:.3f}", 
                           (top_left[0], top_left[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
                
                # 保存匹配结果图
                cv2.imwrite("debug_boss_match.png", screen_marked)
                print("已保存BOSS门匹配结果: debug_boss_match.png")
                
                # 保存匹配热力图
                result_normalized = cv2.normalize(result, None, 0, 255, cv2.NORM_MINMAX, cv2.CV_8U)
                result_colored = cv2.applyColorMap(result_normalized, cv2.COLORMAP_JET)
                cv2.imwrite("debug_boss_heatmap.png", result_colored)
                print("已保存BOSS门匹配热力图: debug_boss_heatmap.png")
                
                if max_val > 0.7:
                    print("✓ BOSS门匹配成功 (阈值 > 0.7)")
                elif max_val > 0.5:
                    print("⚠ BOSS门匹配度中等 (0.5-0.7)")
                else:
                    print("✗ BOSS门匹配度低 (< 0.5)")
            else:
                print("错误: 无法加载BOSS门模板")
        
        print("\n=== 调试完成 ===")
        print("请查看生成的调试文件:")
        print("- debug_current_screen.png: 当前画面")
        print("- debug_normal_match.png: 普通门匹配结果")
        print("- debug_normal_heatmap.png: 普通门匹配热力图")
        print("- debug_boss_match.png: BOSS门匹配结果")
        print("- debug_boss_heatmap.png: BOSS门匹配热力图")
        
        print("\n建议:")
        print("1. 如果匹配度 < 0.5，可能需要重新制作模板")
        print("2. 如果匹配度在 0.5-0.7，可以降低阈值")
        print("3. 如果匹配度 > 0.7，说明模板很好")

def main():
    """主函数"""
    print("门匹配调试工具")
    print("=" * 30)
    
    debugger = DoorMatchDebugger()
    
    if not debugger.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    print("请确保角色在地下城房间中，画面中有门")
    input("准备好后按回车开始调试...")
    
    debugger.debug_door_matching()

if __name__ == "__main__":
    main()
