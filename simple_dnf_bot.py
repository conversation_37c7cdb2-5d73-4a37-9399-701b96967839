#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版DNF搬砖脚本
只使用Python标准库和Windows API，无需额外依赖
"""

import time
import ctypes
from ctypes import wintypes
import configparser
import logging
import os
from datetime import datetime

# Windows API常量
VK_CODE = {
    'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46,
    'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A, 'k': 0x4B, 'l': 0x4C,
    'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
    's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58,
    'y': 0x59, 'z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
    '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
    'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74,
    'f6': 0x75, 'f7': 0x76, 'f8': 0x77, 'f9': 0x78, 'f10': 0x79,
    'f11': 0x7A, 'f12': 0x7B,
    'space': 0x20, 'enter': 0x0D, 'esc': 0x1B, 'tab': 0x09,
    'shift': 0x10, 'ctrl': 0x11, 'alt': 0x12,
    'left': 0x25, 'up': 0x26, 'right': 0x27, 'down': 0x28,
}

class SimpleDNFBot:
    """简化版DNF搬砖机器人"""
    
    def __init__(self):
        self.setup_logging()
        self.load_config()
        self.running = False
        self.start_time = None
        
        # Windows API
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        
    def setup_logging(self):
        """设置日志"""
        os.makedirs("logs", exist_ok=True)
        log_file = f"logs/simple_dnf_bot_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """加载配置"""
        self.config = configparser.ConfigParser()
        if os.path.exists('config.ini'):
            self.config.read('config.ini', encoding='utf-8')
        
        # 获取配置值
        self.work_hours = self.config.getint('GENERAL', 'work_hours', fallback=8)
        self.skill_key = self.config.get('GENERAL', 'skill_key', fallback='e')
        self.logger.info(f"配置加载完成 - 工作时长: {self.work_hours}小时, 技能键: {self.skill_key}")
    
    def find_game_window(self):
        """查找游戏窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("地下城与勇士" in window_title or
                        "DNF" in window_title or
                        "Dungeon" in window_title or
                        "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        self.logger.info(f"找到游戏窗口: {window_title}")
                        return False  # 停止枚举
            return True
        
        self.game_hwnd = None
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def activate_window(self):
        """激活游戏窗口"""
        if self.game_hwnd:
            self.user32.SetForegroundWindow(self.game_hwnd)
            self.user32.ShowWindow(self.game_hwnd, 9)  # SW_RESTORE
            time.sleep(0.1)
            return True
        return False
    
    def press_key(self, key, duration=0.05):
        """按键 - 直接发送到游戏窗口"""
        if not self.game_hwnd:
            self.logger.error("游戏窗口句柄无效")
            return False

        vk_code = VK_CODE.get(key.lower())
        if vk_code is None:
            self.logger.error(f"未知按键: {key}")
            return False

        try:
            # 激活窗口
            self.activate_window()
            time.sleep(0.1)

            # 计算扫描码
            scan_code = self.user32.MapVirtualKeyW(vk_code, 0)

            # 发送WM_KEYDOWN消息
            WM_KEYDOWN = 0x0100
            WM_KEYUP = 0x0101

            # 构造lParam
            lParam_down = (scan_code << 16) | 1
            lParam_up = (scan_code << 16) | 0xC0000001

            # 发送按键消息到游戏窗口
            self.user32.PostMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)
            time.sleep(duration)
            self.user32.PostMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)

            self.logger.info(f"发送按键: {key} (VK:{vk_code}) 到窗口 {self.game_hwnd}")
            time.sleep(0.01)
            return True
        except Exception as e:
            self.logger.error(f"按键失败 {key}: {e}")
            return False
    
    def press_combination(self, keys, duration=0.05):
        """按组合键 - 直接发送到游戏窗口"""
        if not self.game_hwnd:
            self.logger.error("游戏窗口句柄无效")
            return False

        vk_codes = []
        for key in keys:
            vk_code = VK_CODE.get(key.lower())
            if vk_code is None:
                self.logger.error(f"未知按键: {key}")
                return False
            vk_codes.append(vk_code)

        try:
            # 激活窗口
            self.activate_window()
            time.sleep(0.1)

            WM_KEYDOWN = 0x0100
            WM_KEYUP = 0x0101

            # 按下所有键
            for vk_code in vk_codes:
                scan_code = self.user32.MapVirtualKeyW(vk_code, 0)
                lParam_down = (scan_code << 16) | 1
                self.user32.PostMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)

            time.sleep(duration)

            # 释放所有键（逆序）
            for vk_code in reversed(vk_codes):
                scan_code = self.user32.MapVirtualKeyW(vk_code, 0)
                lParam_up = (scan_code << 16) | 0xC0000001
                self.user32.PostMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)

            self.logger.info(f"发送组合键: {keys} 到窗口 {self.game_hwnd}")
            time.sleep(0.01)
            return True
        except Exception as e:
            self.logger.error(f"组合键失败 {keys}: {e}")
            return False
    
    def basic_farming_cycle(self):
        """基础搬砖循环"""
        self.logger.info("开始基础搬砖循环")

        # 等待用户确认
        input("请确保角色在城镇，然后按回车开始...")

        # 1. 按右键进入地图选择
        self.logger.info("步骤1: 进入地图选择")
        self.press_key('right')
        time.sleep(3)

        # 2. 按空格确认进入（假设已选择格兰迪）
        self.logger.info("步骤2: 确认进入地图")
        self.press_key('space')
        time.sleep(5)  # 等待加载

        # 3. 打怪循环（简化版）
        for room in range(8):  # 假设8个房间
            self.logger.info(f"步骤3.{room+1}: 清理房间 {room + 1}")

            # 移动到房间右侧
            self.logger.info("  - 向右移动")
            self.press_key('right', duration=1.0)
            time.sleep(1.0)

            # 释放技能攻击
            self.logger.info(f"  - 释放技能 {self.skill_key}")
            for i in range(5):
                self.press_key(self.skill_key)
                time.sleep(0.2)

            # 等待怪物死亡
            self.logger.info("  - 等待怪物死亡")
            time.sleep(2)

            # 顺图到下一房间
            if room < 7:  # 不是最后一个房间
                self.logger.info("  - 顺图到下一房间")
                self.press_combination(['alt', 'right'])
                time.sleep(2.0)

        # 4. 完成后按ESC退出
        self.logger.info("步骤4: 地图完成，准备退出")
        time.sleep(3)

        # 多次按ESC确保退出
        for i in range(3):
            self.logger.info(f"  - 按ESC第{i+1}次")
            self.press_key('esc')
            time.sleep(1.5)

        # 5. 按数字键4（可能是某个功能）
        self.logger.info("步骤5: 按数字键4")
        self.press_key('4')
        time.sleep(2)
    
    def start(self):
        """启动机器人"""
        self.logger.info("=" * 50)
        self.logger.info("简化版DNF搬砖机器人启动")
        self.logger.info("=" * 50)
        
        if not self.find_game_window():
            self.logger.error("未找到DNF游戏窗口，请确保游戏已启动")
            return
        
        self.running = True
        self.start_time = datetime.now()
        work_duration = self.work_hours * 3600  # 转换为秒
        
        cycle_count = 0
        
        try:
            while self.running:
                # 检查工作时长
                elapsed = (datetime.now() - self.start_time).total_seconds()
                if elapsed >= work_duration:
                    self.logger.info("已达到设定工作时长，程序结束")
                    break
                
                cycle_count += 1
                self.logger.info(f"开始第 {cycle_count} 轮搬砖")
                
                # 执行搬砖循环
                self.basic_farming_cycle()
                
                # 等待一段时间再开始下一轮
                self.logger.info("等待下一轮...")
                time.sleep(5)
                
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"程序异常: {e}")
        finally:
            self.running = False
            self.logger.info("简化版DNF搬砖机器人已停止")

def main():
    """主函数"""
    print("简化版DNF搬砖脚本")
    print("=" * 30)
    print("功能说明:")
    print("- 自动进入地图")
    print("- 自动打怪清房间")
    print("- 自动顺图")
    print("- 自动退出和重复")
    print()
    print("使用前请确保:")
    print("1. DNF游戏已启动并登录")
    print("2. 角色位于城镇")
    print("3. 以管理员权限运行此脚本")
    print("=" * 30)
    
    input("按回车键开始运行...")
    
    bot = SimpleDNFBot()
    bot.start()

if __name__ == "__main__":
    main()
