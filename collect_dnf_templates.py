#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF正确的模板收集工具
基于正确的DNF机制：房间格子闪烁判断
"""

import ctypes
import time
import os
import cv2
import numpy as np
from PIL import ImageGrab
from ctypes import wintypes, byref

class DNFTemplateCollector:
    """DNF模板收集器"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.window_rect = None
        
        # 创建模板目录
        os.makedirs("templates", exist_ok=True)
        
        # 正确的DNF模板列表
        self.templates_needed = [
            # === 核心判断机制 ===
            ("minimap_current_room", "当前房间格子 - 请截取小地图中当前房间的格子"),
            ("minimap_next_room_dark", "下个房间格子(暗) - 请在有怪时截取下个房间的暗格子"),
            ("minimap_next_room_bright", "下个房间格子(亮) - 请在无怪时截取下个房间的闪烁格子"),
            
            # === 游戏状态 ===
            ("town_interface", "城镇界面 - 请截取城镇特有的界面元素"),
            ("map_selection_interface", "地图选择界面 - 请截取地图选择的标题"),
            ("in_dungeon_interface", "地下城界面 - 请截取地下城内的界面特征"),
            
            # === 地图选择 ===
            ("grandi_option", "格兰迪选项 - 请截取格兰迪的文字或图标"),
            ("hell_difficulty", "地狱难度 - 请截取地狱难度选项"),
            ("abyss_mode_button", "深渊模式按钮 - 请截取深渊模式按钮"),
            ("enter_dungeon_button", "进入地下城按钮 - 请截取确认进入按钮"),
            
            # === 深渊机制 ===
            ("abyss_pillar_normal", "深渊柱子(正常) - 请截取正常状态的深渊柱子"),
            ("abyss_pillar_activated", "深渊柱子(激活) - 请截取发白光的深渊柱子"),
            ("abyss_wave_indicator", "深渊波数指示 - 请截取显示当前波数的区域"),
            ("abyss_complete_effect", "深渊完成特效 - 请截取深渊完成时的特效"),
            ("abyss_room_clear", "深渊房间清空 - 请截取深渊房间清空后的状态"),
            
            # === 房间类型 ===
            ("normal_room", "普通房间 - 请截取普通房间的特征"),
            ("boss_room", "BOSS房间 - 请截取BOSS房间的特征"),
            ("treasure_room", "宝箱房间 - 请截取宝箱房间的特征"),
            
            # === 完成判断 ===
            ("dungeon_clear", "地下城通关 - 请截取通关界面"),
            ("flip_card", "翻牌界面 - 请截取翻牌界面"),
            ("return_town", "返回城镇 - 请截取返回城镇按钮"),
            
            # === 背包管理 ===
            ("inventory_full", "背包满 - 请截取背包满的提示"),
            ("equipment_durability", "装备耐久 - 请截取装备耐久警告"),
            ("stall_decompose", "摆摊分解 - 请截取摆摊分解界面"),
        ]
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        print(f"找到DNF窗口: {window_title}")
                        print(f"窗口大小: {rect.right-rect.left} x {rect.bottom-rect.top}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def capture_game_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.2)
            
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"截屏失败: {e}")
            return None
    
    def detect_brightness_change(self, image1, image2, threshold=30):
        """检测亮度变化（用于检测闪烁）"""
        # 转换为灰度图
        gray1 = cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY)
        
        # 计算平均亮度
        brightness1 = np.mean(gray1)
        brightness2 = np.mean(gray2)
        
        # 计算亮度差异
        brightness_diff = abs(brightness1 - brightness2)
        
        return brightness_diff > threshold, brightness_diff
    
    def test_room_flicker(self):
        """测试房间格子闪烁检测"""
        print("\n=== 房间格子闪烁检测测试 ===")
        print("请确保当前在地下城中，且下个房间格子可见")
        
        # 检查是否有下个房间模板
        next_room_path = "templates/minimap_next_room_bright.png"
        if not os.path.exists(next_room_path):
            print("请先收集'下个房间格子(亮)'模板")
            return
        
        next_room_template = cv2.imread(next_room_path)
        
        print("开始检测闪烁...")
        print("将连续截取3张图片检测亮度变化")
        
        images = []
        for i in range(3):
            print(f"截取第{i+1}张图片...")
            screen = self.capture_game_screen()
            if screen is not None:
                # 找到下个房间位置
                result = cv2.matchTemplate(screen, next_room_template, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                
                if max_val > 0.7:
                    h, w = next_room_template.shape[:2]
                    room_region = screen[max_loc[1]:max_loc[1]+h, max_loc[0]:max_loc[0]+w]
                    images.append(room_region)
                else:
                    print("未找到下个房间格子")
                    return
            
            time.sleep(0.5)  # 等待0.5秒
        
        if len(images) >= 2:
            # 检测闪烁
            is_flickering, diff = self.detect_brightness_change(images[0], images[1])
            
            print(f"亮度差异: {diff:.2f}")
            print(f"判断结果: {'房间可通行(闪烁)' if is_flickering else '房间不可通行(不闪烁)'}")
            
            # 显示对比
            cv2.imshow("第1张图片", images[0])
            cv2.imshow("第2张图片", images[1])
            cv2.waitKey(3000)
            cv2.destroyAllWindows()
    
    def save_template_with_guide(self, name, description):
        """保存模板并提供指导"""
        print(f"\n=== 收集模板: {name} ===")
        print(f"说明: {description}")
        
        # 特殊指导
        if "next_room" in name:
            print("\n⚠️ 重要提示:")
            if "dark" in name:
                print("请在有怪物的房间中截取下个房间的格子")
                print("此时下个房间格子应该是暗的，不会闪烁")
            else:
                print("请在无怪物的房间中截取下个房间的格子")
                print("此时下个房间格子应该是亮的，会闪烁")
            print("这是判断房间是否清空的关键！")
        
        elif "abyss_pillar" in name:
            print("\n⚠️ 深渊柱子提示:")
            if "normal" in name:
                print("请截取正常状态的深渊柱子（未激活）")
                print("确保柱子清晰可见，没有白光效果")
            elif "activated" in name:
                print("请先攻击柱子，然后截取发白光的柱子")
                print("这是判断柱子已被激活的关键状态")
            print("深渊机制：攻击柱子→柱子发白光→刷怪→重复3次")
        
        while True:
            choice = input("\n输入 's' 截图，'test' 测试闪烁检测，'skip' 跳过: ").strip().lower()
            
            if choice == 's':
                screen = self.capture_game_screen()
                if screen is not None:
                    self.select_region_and_save(screen, name, description)
                    break
                else:
                    print("截图失败，请重试")
            elif choice == 'test':
                self.test_room_flicker()
            elif choice == 'skip':
                print(f"跳过 {name}")
                break
            else:
                print("无效输入")
    
    def select_region_and_save(self, screen, name, description):
        """选择区域并保存"""
        print(f"\n在弹出窗口中选择 {name} 区域:")
        print("- 鼠标拖拽选择区域")
        print("- 空格键确认保存")
        print("- ESC键取消")
        
        # 创建窗口
        window_name = f"选择区域 - {name}"
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_name, 1200, 800)
        
        # 鼠标选择变量
        self.selecting = False
        self.start_point = None
        self.end_point = None
        
        def mouse_callback(event, x, y, flags, param):
            if event == cv2.EVENT_LBUTTONDOWN:
                self.selecting = True
                self.start_point = (x, y)
                self.end_point = (x, y)
            elif event == cv2.EVENT_MOUSEMOVE and self.selecting:
                self.end_point = (x, y)
                temp_screen = screen.copy()
                cv2.rectangle(temp_screen, self.start_point, self.end_point, (0, 255, 0), 2)
                cv2.imshow(window_name, temp_screen)
            elif event == cv2.EVENT_LBUTTONUP:
                self.selecting = False
        
        cv2.setMouseCallback(window_name, mouse_callback)
        cv2.imshow(window_name, screen)
        
        while True:
            key = cv2.waitKey(1) & 0xFF
            if key == ord(' '):  # 空格确认
                if self.start_point and self.end_point:
                    x1, y1 = self.start_point
                    x2, y2 = self.end_point
                    
                    x1, x2 = min(x1, x2), max(x1, x2)
                    y1, y2 = min(y1, y2), max(y1, y2)
                    
                    if x2 - x1 > 5 and y2 - y1 > 5:
                        template = screen[y1:y2, x1:x2]
                        
                        # 保存模板
                        filename = f"templates/{name}.png"
                        cv2.imwrite(filename, template)
                        print(f"✓ 已保存: {filename}")
                        print(f"  大小: {x2-x1} x {y2-y1}")
                        
                        # 显示保存的模板
                        cv2.imshow(f"已保存 - {name}", template)
                        cv2.waitKey(2000)
                        cv2.destroyAllWindows()
                        break
                    else:
                        print("区域太小，请重新选择")
            elif key == 27:  # ESC取消
                cv2.destroyAllWindows()
                break
    
    def show_core_templates(self):
        """显示核心模板"""
        print("\n" + "=" * 60)
        print("🔥 DNF核心模板（基于正确的游戏机制）")
        print("=" * 60)
        
        core_templates = [
            ("minimap_next_room_dark", "下个房间格子(暗)", "有怪时不闪烁"),
            ("minimap_next_room_bright", "下个房间格子(亮)", "无怪时闪烁"),
            ("grandi_option", "格兰迪选项", "选择正确地下城"),
            ("hell_difficulty", "地狱难度", "选择正确难度"),
            ("abyss_pillar_normal", "深渊柱子(正常)", "识别未激活的柱子"),
            ("abyss_pillar_activated", "深渊柱子(激活)", "识别发白光的柱子"),
            ("dungeon_clear", "地下城通关", "完成判断"),
            ("town_interface", "城镇界面", "返回城镇判断"),
        ]
        
        print("基于您的指正，正确的判断逻辑是:")
        print("✓ 有怪物 → 下个房间格子暗淡(不闪烁)")
        print("✓ 无怪物 → 下个房间格子闪烁(可通行)")
        print()
        
        for i, (name, title, purpose) in enumerate(core_templates, 1):
            status = "✓" if os.path.exists(f"templates/{name}.png") else "✗"
            print(f"{i}. {status} {title}")
            print(f"   用途: {purpose}")
            print()
    
    def collect_core_templates(self):
        """收集核心模板"""
        core_list = [
            ("minimap_next_room_dark", "下个房间格子(暗) - 请在有怪时截取下个房间的暗格子"),
            ("minimap_next_room_bright", "下个房间格子(亮) - 请在无怪时截取下个房间的闪烁格子"),
            ("grandi_option", "格兰迪选项 - 请截取格兰迪的文字或图标"),
            ("hell_difficulty", "地狱难度 - 请截取地狱难度选项"),
            ("abyss_pillar_normal", "深渊柱子(正常) - 请截取正常状态的深渊柱子"),
            ("abyss_pillar_activated", "深渊柱子(激活) - 请截取发白光的深渊柱子"),
            ("dungeon_clear", "地下城通关 - 请截取通关界面"),
            ("town_interface", "城镇界面 - 请截取城镇特有界面"),
        ]
        
        print("开始收集核心模板...")
        print("基于正确的DNF机制：房间格子闪烁判断")
        
        for name, description in core_list:
            if os.path.exists(f"templates/{name}.png"):
                choice = input(f"\n{name}.png 已存在，是否重新收集? (y/n): ").strip().lower()
                if choice != 'y':
                    continue
            
            self.save_template_with_guide(name, description)

def main():
    """主函数"""
    collector = DNFTemplateCollector()
    
    if not collector.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    while True:
        print("\n" + "=" * 50)
        print("DNF正确的模板收集工具")
        print("基于正确机制：房间格子闪烁判断")
        print("=" * 50)
        print("1. 查看核心模板说明")
        print("2. 收集核心模板")
        print("3. 测试房间闪烁检测")
        print("4. 收集所有模板")
        print("5. 查看已收集模板")
        print("0. 退出")
        
        choice = input("\n请选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            collector.show_core_templates()
        elif choice == '2':
            collector.collect_core_templates()
        elif choice == '3':
            collector.test_room_flicker()
        elif choice == '4':
            for name, desc in collector.templates_needed:
                if not os.path.exists(f"templates/{name}.png"):
                    collector.save_template_with_guide(name, desc)
        elif choice == '5':
            template_dir = "templates"
            if os.path.exists(template_dir):
                templates = [f for f in os.listdir(template_dir) if f.endswith('.png')]
                print(f"\n已收集 {len(templates)} 个模板:")
                for template in sorted(templates):
                    print(f"✓ {template}")
            else:
                print("没有找到模板")
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
