#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF搬砖脚本 - SendInput版本
使用SendInput API进行键盘模拟
"""

import ctypes
import time
import configparser
import os
from ctypes import wintypes, Structure, Union, POINTER, byref

# Windows API常量
INPUT_KEYBOARD = 1
KEYEVENTF_EXTENDEDKEY = 0x0001
KEYEVENTF_KEYUP = 0x0002
KEYEVENTF_SCANCODE = 0x0008
MAPVK_VK_TO_VSC = 0

# 定义结构体
class KEYBDINPUT(Structure):
    _fields_ = [
        ("wVk", wintypes.WORD),
        ("wScan", wintypes.WORD),
        ("dwFlags", wintypes.DWORD),
        ("time", wintypes.DWORD),
        ("dwExtraInfo", POINTER(wintypes.ULONG))
    ]

class INPUT_UNION(Union):
    _fields_ = [("ki", KEYBDINPUT)]

class INPUT(Structure):
    _fields_ = [
        ("type", wintypes.DWORD),
        ("ii", INPUT_UNION)
    ]

# 虚拟键码
VK_CODES = {
    'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46,
    'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A, 'k': 0x4B, 'l': 0x4C,
    'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
    's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58,
    'y': 0x59, 'z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
    '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
    'space': 0x20, 'enter': 0x0D, 'esc': 0x1B, 'tab': 0x09,
    'shift': 0x10, 'ctrl': 0x11, 'alt': 0x12,
    'left': 0x25, 'up': 0x26, 'right': 0x27, 'down': 0x28,
}

class DNFBotSendInput:
    """DNF搬砖机器人 - SendInput版本"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.skill_key = 'e'
        self.running = False
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            print(f"技能键设置: {self.skill_key}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or 
                        "Fighter" in window_title or
                        "地下城与勇士" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def activate_window(self):
        """激活窗口"""
        if self.game_hwnd:
            self.user32.ShowWindow(self.game_hwnd, 9)
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.2)
            return True
        return False
    
    def send_key(self, key, duration=0.05):
        """发送按键 - 使用SendInput"""
        vk_code = VK_CODES.get(key.lower())
        if not vk_code:
            print(f"未知按键: {key}")
            return False
        
        # 激活窗口
        self.activate_window()
        
        # 判断是否为扩展键
        extended_keys = ['left', 'right', 'up', 'down', 'ctrl', 'alt']
        is_extended = key.lower() in extended_keys
        
        try:
            # 创建输入结构
            inputs = (INPUT * 2)()
            
            # 按下事件
            inputs[0].type = INPUT_KEYBOARD
            inputs[0].ii.ki.wVk = vk_code
            inputs[0].ii.ki.wScan = self.user32.MapVirtualKeyW(vk_code, MAPVK_VK_TO_VSC)
            inputs[0].ii.ki.dwFlags = KEYEVENTF_EXTENDEDKEY if is_extended else 0
            inputs[0].ii.ki.time = 0
            inputs[0].ii.ki.dwExtraInfo = None
            
            # 释放事件
            inputs[1].type = INPUT_KEYBOARD
            inputs[1].ii.ki.wVk = vk_code
            inputs[1].ii.ki.wScan = self.user32.MapVirtualKeyW(vk_code, MAPVK_VK_TO_VSC)
            inputs[1].ii.ki.dwFlags = (KEYEVENTF_EXTENDEDKEY if is_extended else 0) | KEYEVENTF_KEYUP
            inputs[1].ii.ki.time = 0
            inputs[1].ii.ki.dwExtraInfo = None
            
            # 发送输入
            result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
            
            if result == 2:
                print(f"✓ 发送按键: {key}")
                return True
            else:
                print(f"✗ 发送按键失败: {key} (结果: {result})")
                return False
                
        except Exception as e:
            print(f"发送按键异常: {e}")
            return False
    
    def send_combination(self, keys, duration=0.05):
        """发送组合键"""
        vk_codes = []
        for key in keys:
            vk_code = VK_CODES.get(key.lower())
            if vk_code:
                vk_codes.append(vk_code)
            else:
                print(f"未知按键: {key}")
                return False
        
        # 激活窗口
        self.activate_window()
        
        try:
            # 创建输入数组
            input_count = len(vk_codes) * 2
            inputs = (INPUT * input_count)()
            
            # 按下所有键
            for i, vk_code in enumerate(vk_codes):
                inputs[i].type = INPUT_KEYBOARD
                inputs[i].ii.ki.wVk = vk_code
                inputs[i].ii.ki.wScan = 0
                inputs[i].ii.ki.dwFlags = 0
                inputs[i].ii.ki.time = 0
                inputs[i].ii.ki.dwExtraInfo = None
            
            # 释放所有键（逆序）
            for i, vk_code in enumerate(reversed(vk_codes)):
                idx = len(vk_codes) + i
                inputs[idx].type = INPUT_KEYBOARD
                inputs[idx].ii.ki.wVk = vk_code
                inputs[idx].ii.ki.wScan = 0
                inputs[idx].ii.ki.dwFlags = KEYEVENTF_KEYUP
                inputs[idx].ii.ki.time = 0
                inputs[idx].ii.ki.dwExtraInfo = None
            
            # 发送输入
            result = self.user32.SendInput(input_count, inputs, ctypes.sizeof(INPUT))
            
            if result == input_count:
                print(f"✓ 发送组合键: {'+'.join(keys)}")
                return True
            else:
                print(f"✗ 发送组合键失败: {'+'.join(keys)}")
                return False
                
        except Exception as e:
            print(f"发送组合键异常: {e}")
            return False
    
    def farming_cycle(self):
        """搬砖循环"""
        print("开始搬砖循环...")
        
        # 1. 进入地图选择
        print("1. 进入地图选择")
        self.send_key('right')
        time.sleep(2)
        
        # 2. 确认进入地图
        print("2. 确认进入地图")
        self.send_key('space')
        time.sleep(3)
        
        # 3. 打怪循环
        for room in range(8):
            print(f"3.{room+1} 清理房间 {room+1}")
            
            # 向右移动
            print("  - 向右移动")
            self.send_key('right', duration=1.0)
            time.sleep(1)
            
            # 释放技能
            print(f"  - 释放技能 {self.skill_key}")
            for _ in range(5):
                self.send_key(self.skill_key)
                time.sleep(0.2)
            
            # 等待怪物死亡
            time.sleep(1.5)
            
            # 顺图到下一房间
            if room < 7:
                print("  - 顺图")
                self.send_combination(['alt', 'right'])
                time.sleep(2)
        
        # 4. 退出地图
        print("4. 退出地图")
        for i in range(3):
            print(f"  - 按ESC第{i+1}次")
            self.send_key('esc')
            time.sleep(1)
        
        # 5. 按数字键4
        print("5. 按数字键4")
        self.send_key('4')
        time.sleep(2)
        
        print("搬砖循环完成!")
    
    def start_bot(self):
        """启动机器人"""
        if not self.find_dnf_window():
            print("错误: 未找到DNF窗口")
            return
        
        print("DNF搬砖机器人已启动 (SendInput版本)")
        print("按Ctrl+C停止")
        
        self.running = True
        cycle_count = 0
        
        try:
            while self.running:
                cycle_count += 1
                print(f"\n=== 第 {cycle_count} 轮搬砖 ===")
                
                self.farming_cycle()
                
                print("等待下一轮...")
                time.sleep(3)
                
        except KeyboardInterrupt:
            print("\n用户停止机器人")
        except Exception as e:
            print(f"机器人异常: {e}")
        finally:
            self.running = False
            print("机器人已停止")

def main():
    """主函数"""
    print("DNF搬砖机器人 - SendInput版本")
    print("=" * 40)
    
    bot = DNFBotSendInput()
    
    while True:
        print("\n请选择操作:")
        print("1. 启动自动搬砖")
        print("2. 测试按键功能")
        print("3. 单次搬砖循环")
        print("0. 退出")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            print("\n请确保:")
            print("- DNF角色在城镇")
            print("- 没有打开对话框")
            print("- 准备好开始搬砖")
            input("\n按回车开始...")
            bot.start_bot()
        elif choice == '2':
            if not bot.find_dnf_window():
                print("未找到DNF窗口")
                continue
            
            key = input("请输入要测试的按键: ").strip()
            if key:
                bot.send_key(key)
        elif choice == '3':
            if not bot.find_dnf_window():
                print("未找到DNF窗口")
                continue
            
            print("执行单次搬砖循环...")
            bot.farming_cycle()
        else:
            print("无效选择")
    
    print("程序结束")

if __name__ == "__main__":
    main()
