#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF按键输入测试工具
用于诊断按键输入问题
"""

import ctypes
import time
from ctypes import wintypes, byref

# 扫描码（使用之前验证有效的版本）
HARDWARE_SCAN_CODES = {
    'e': 0x12, 'space': 0x39, 'esc': 0x01, '4': 0x05,
    'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0,
    'alt': 0x38, 'f11': 0x57, '0': 0x0B,
}

class InputTester:
    """按键输入测试器"""
    
    def __init__(self):
        self.kernel32 = ctypes.windll.kernel32
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        
        self.enable_debug_privilege()
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                print("✓ 调试权限已启用")
        except Exception as e:
            print(f"调试权限启用失败: {e}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def send_key_method1(self, key):
        """方法1: keybd_event + 扫描码"""
        scan_code = HARDWARE_SCAN_CODES.get(key.lower())
        if not scan_code:
            print(f"未知按键: {key}")
            return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            print(f"方法1: keybd_event发送 {key} (扫描码: 0x{scan_code:02X})")
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            return True
        except Exception as e:
            print(f"方法1失败: {e}")
            return False
    
    def send_key_method2(self, key):
        """方法2: SendInput + 扫描码"""
        scan_code = HARDWARE_SCAN_CODES.get(key.lower())
        if not scan_code:
            return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            from ctypes import Structure, Union, POINTER
            
            class KEYBDINPUT(Structure):
                _fields_ = [("wVk", wintypes.WORD), ("wScan", wintypes.WORD),
                           ("dwFlags", wintypes.DWORD), ("time", wintypes.DWORD),
                           ("dwExtraInfo", POINTER(wintypes.ULONG))]
            
            class INPUT_UNION(Union):
                _fields_ = [("ki", KEYBDINPUT)]
            
            class INPUT(Structure):
                _fields_ = [("type", wintypes.DWORD), ("ii", INPUT_UNION)]
            
            inputs = (INPUT * 2)()
            
            # 按下
            inputs[0].type = 1
            inputs[0].ii.ki.wVk = 0
            inputs[0].ii.ki.wScan = scan_code
            inputs[0].ii.ki.dwFlags = 0x0008
            inputs[0].ii.ki.time = 0
            inputs[0].ii.ki.dwExtraInfo = None
            
            # 释放
            inputs[1].type = 1
            inputs[1].ii.ki.wVk = 0
            inputs[1].ii.ki.wScan = scan_code
            inputs[1].ii.ki.dwFlags = 0x0008 | 0x0002
            inputs[1].ii.ki.time = 0
            inputs[1].ii.ki.dwExtraInfo = None
            
            print(f"方法2: SendInput发送 {key} (扫描码: 0x{scan_code:02X})")
            result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
            return result == 2
            
        except Exception as e:
            print(f"方法2失败: {e}")
            return False
    
    def send_key_method3(self, key):
        """方法3: PostMessage"""
        scan_code = HARDWARE_SCAN_CODES.get(key.lower())
        if not scan_code:
            return False
        
        try:
            WM_KEYDOWN = 0x0100
            WM_KEYUP = 0x0101
            
            # 转换为虚拟键码
            vk_code = self.user32.MapVirtualKeyW(scan_code, 1)
            
            lParam_down = (scan_code << 16) | 1
            lParam_up = (scan_code << 16) | 0xC0000001
            
            print(f"方法3: PostMessage发送 {key} (VK: {vk_code}, SC: 0x{scan_code:02X})")
            result1 = self.user32.PostMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)
            time.sleep(0.05)
            result2 = self.user32.PostMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)
            
            return result1 and result2
            
        except Exception as e:
            print(f"方法3失败: {e}")
            return False
    
    def test_all_methods(self, key='right'):
        """测试所有方法"""
        print(f"\n=== 测试按键: {key} ===")
        print("请观察角色是否移动")
        
        methods = [
            ("方法1: keybd_event", self.send_key_method1),
            ("方法2: SendInput", self.send_key_method2),
            ("方法3: PostMessage", self.send_key_method3),
        ]
        
        for name, method in methods:
            print(f"\n{name}")
            try:
                result = method(key)
                print(f"  发送结果: {'成功' if result else '失败'}")
                print("  请观察角色是否移动...")
                time.sleep(3)  # 给时间观察
                
                response = input("  角色是否移动了? (y/n): ").strip().lower()
                if response == 'y':
                    print(f"  ✓ {name} 有效!")
                    return name, method
                else:
                    print(f"  ✗ {name} 无效")
            except Exception as e:
                print(f"  异常: {e}")
        
        print("\n所有方法都无效")
        return None, None
    
    def continuous_test(self, method_func, key='right'):
        """连续测试"""
        print(f"\n=== 连续测试 {key} 键 ===")
        print("将连续发送5次按键")
        
        for i in range(5):
            print(f"第{i+1}次发送...")
            method_func(key)
            time.sleep(1)
        
        print("连续测试完成")
    
    def interactive_test(self):
        """交互式测试"""
        while True:
            print("\n" + "=" * 40)
            print("DNF按键输入测试工具")
            print("=" * 40)
            print("1. 测试所有方法")
            print("2. 测试方法1 (keybd_event)")
            print("3. 测试方法2 (SendInput)")
            print("4. 测试方法3 (PostMessage)")
            print("5. 连续测试")
            print("6. 自定义按键测试")
            print("0. 退出")
            
            choice = input("\n请选择: ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                key = input("请输入要测试的按键 (默认right): ").strip() or 'right'
                self.test_all_methods(key)
            elif choice == '2':
                key = input("请输入要测试的按键 (默认right): ").strip() or 'right'
                self.send_key_method1(key)
                time.sleep(2)
            elif choice == '3':
                key = input("请输入要测试的按键 (默认right): ").strip() or 'right'
                self.send_key_method2(key)
                time.sleep(2)
            elif choice == '4':
                key = input("请输入要测试的按键 (默认right): ").strip() or 'right'
                self.send_key_method3(key)
                time.sleep(2)
            elif choice == '5':
                print("请先选择有效的方法:")
                print("1. keybd_event")
                print("2. SendInput")
                print("3. PostMessage")
                method_choice = input("选择方法: ").strip()
                
                if method_choice == '1':
                    self.continuous_test(self.send_key_method1)
                elif method_choice == '2':
                    self.continuous_test(self.send_key_method2)
                elif method_choice == '3':
                    self.continuous_test(self.send_key_method3)
            elif choice == '6':
                key = input("请输入按键名称: ").strip()
                if key in HARDWARE_SCAN_CODES:
                    print("测试所有方法...")
                    self.test_all_methods(key)
                else:
                    print("无效按键")
            else:
                print("无效选择")

def main():
    """主函数"""
    print("DNF按键输入测试工具")
    print("=" * 30)
    print("用于诊断按键输入问题")
    
    tester = InputTester()
    
    if not tester.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    print("\n请确保:")
    print("1. DNF角色在可以移动的地方")
    print("2. 没有对话框或菜单打开")
    print("3. 角色没有被控制限制")
    
    tester.interactive_test()

if __name__ == "__main__":
    main()
