#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF门识别测试工具
专门测试门的识别和移动功能
"""

import ctypes
import time
import cv2
import numpy as np
from PIL import ImageGrab
from ctypes import wintypes, byref

class DoorDetectionTester:
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.window_rect = None
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        print(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def capture_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            screen = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            return screen
        except Exception as e:
            print(f"截取画面失败: {e}")
            return None
    
    def find_door_lights(self):
        """寻找门灯（蓝灯=普通房，黄灯=BOSS房）"""
        screen = self.capture_screen()
        if screen is None:
            return []

        print("开始门灯识别...")
        h, w = screen.shape[:2]
        print(f"画面尺寸: {w} x {h}")

        # 保存原始截图
        cv2.imwrite("debug_screen.png", screen)
        print("已保存原始截图: debug_screen.png")

        # 转换为HSV色彩空间
        hsv = cv2.cvtColor(screen, cv2.COLOR_BGR2HSV)

        doors = []

        # 蓝灯检测（普通房间）
        print("\n检测蓝灯（普通房间）...")
        blue_lower = np.array([100, 50, 50])
        blue_upper = np.array([130, 255, 255])
        blue_mask = cv2.inRange(hsv, blue_lower, blue_upper)

        # 保存蓝灯检测结果
        cv2.imwrite("debug_blue_lights.png", blue_mask)
        print("已保存蓝灯检测: debug_blue_lights.png")

        # 寻找蓝灯轮廓
        contours, _ = cv2.findContours(blue_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"找到 {len(contours)} 个蓝色区域")

        blue_count = 0
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 50:  # 门灯的最小面积
                x, y, w_door, h_door = cv2.boundingRect(contour)

                # 检查是否在画面右侧
                if x > w * 0.4:  # 在画面右半部分
                    blue_count += 1
                    door_x = x + w_door // 2
                    door_y = y + h_door // 2
                    print(f"  蓝灯 {blue_count}: 面积={area}, 位置=({door_x}, {door_y}), 区域=({x}, {y}, {w_door}, {h_door})")
                    doors.append((door_x, door_y, area, "蓝灯(普通房)"))

        # 黄灯检测（BOSS房间）
        print("\n检测黄灯（BOSS房间）...")
        yellow_lower = np.array([20, 50, 50])
        yellow_upper = np.array([30, 255, 255])
        yellow_mask = cv2.inRange(hsv, yellow_lower, yellow_upper)

        # 保存黄灯检测结果
        cv2.imwrite("debug_yellow_lights.png", yellow_mask)
        print("已保存黄灯检测: debug_yellow_lights.png")

        # 寻找黄灯轮廓
        contours, _ = cv2.findContours(yellow_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"找到 {len(contours)} 个黄色区域")

        yellow_count = 0
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 50:  # 门灯的最小面积
                x, y, w_door, h_door = cv2.boundingRect(contour)

                # 检查是否在画面右侧
                if x > w * 0.4:  # 在画面右半部分
                    yellow_count += 1
                    door_x = x + w_door // 2
                    door_y = y + h_door // 2
                    print(f"  黄灯 {yellow_count}: 面积={area}, 位置=({door_x}, {door_y}), 区域=({x}, {y}, {w_door}, {h_door})")
                    doors.append((door_x, door_y, area, "黄灯(BOSS房)"))

        # 合并检测结果
        combined_mask = cv2.bitwise_or(blue_mask, yellow_mask)
        cv2.imwrite("debug_all_lights.png", combined_mask)
        print("已保存所有门灯检测: debug_all_lights.png")

        return doors
    
    def visualize_doors(self, doors):
        """可视化门的位置"""
        screen = self.capture_screen()
        if screen is None or not doors:
            return
        
        print(f"\n在画面上标记 {len(doors)} 个可能的门:")
        
        # 在原图上标记所有可能的门
        for i, (x, y, area, method) in enumerate(doors):
            cv2.circle(screen, (x, y), 10, (0, 255, 0), 2)
            cv2.putText(screen, f"{i+1}", (x-5, y-15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            print(f"  门 {i+1}: 位置=({x}, {y}), 面积={area}, 方法={method}")
        
        # 保存标记后的图像
        cv2.imwrite("debug_doors_marked.png", screen)
        print("已保存标记图像: debug_doors_marked.png")
        
        # 显示图像（如果可能）
        try:
            cv2.imshow("门识别结果", screen)
            print("按任意键关闭图像窗口...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        except:
            print("无法显示图像窗口，请查看保存的文件")
    
    def test_door_detection(self):
        """测试门识别"""
        print("=== DNF门识别测试 ===")
        print("请确保角色在地下城房间中")
        input("准备好后按回车开始...")
        
        # 门灯识别
        doors = self.find_door_lights()
        
        if doors:
            print(f"\n总共找到 {len(doors)} 个可能的门")
            
            # 可视化结果
            self.visualize_doors(doors)
            
            # 让用户选择正确的门
            print("\n请查看生成的图像文件，确认哪个是正确的门:")
            for i, (x, y, area, method) in enumerate(doors):
                print(f"  {i+1}. 位置=({x}, {y}), 面积={area}, 检测方法={method}")
            
            try:
                choice = int(input("请输入正确门的编号 (0=无正确门): "))
                if 1 <= choice <= len(doors):
                    correct_door = doors[choice-1]
                    print(f"您选择的门: 位置=({correct_door[0]}, {correct_door[1]})")
                    return correct_door[:2]  # 返回 (x, y)
                else:
                    print("无正确门")
            except:
                print("无效输入")
        else:
            print("未找到任何可能的门")
        
        return None

def main():
    """主函数"""
    print("DNF门识别测试工具")
    print("=" * 30)
    
    tester = DoorDetectionTester()
    
    if not tester.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    while True:
        print("\n请选择操作:")
        print("1. 测试门识别")
        print("2. 截取当前画面")
        print("0. 退出")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            door_pos = tester.test_door_detection()
            if door_pos:
                print(f"识别到的门位置: {door_pos}")
        elif choice == '2':
            screen = tester.capture_screen()
            if screen is not None:
                cv2.imwrite("current_screen.png", screen)
                print("当前画面已保存: current_screen.png")
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
