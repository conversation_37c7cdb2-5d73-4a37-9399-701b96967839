#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试坐标计算是否正确
"""

import ctypes
import time
import cv2
import numpy as np
from PIL import ImageGrab
from ctypes import wintypes, byref

class CoordinateTest:
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.window_rect = None
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        print(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def capture_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            screen = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            return screen
        except Exception as e:
            print(f"截取画面失败: {e}")
            return None
    
    def test_door_coordinates(self):
        """测试门坐标计算"""
        screen = self.capture_screen()
        if screen is None:
            return
        
        print("开始测试门坐标计算...")
        h, w = screen.shape[:2]
        print(f"画面尺寸: {w} x {h}")
        
        # 转换为HSV
        hsv = cv2.cvtColor(screen, cv2.COLOR_BGR2HSV)
        
        # 蓝灯检测
        blue_lower = np.array([100, 50, 50])
        blue_upper = np.array([130, 255, 255])
        blue_mask = cv2.inRange(hsv, blue_lower, blue_upper)
        
        # 黄灯检测
        yellow_lower = np.array([20, 50, 50])
        yellow_upper = np.array([30, 255, 255])
        yellow_mask = cv2.inRange(hsv, yellow_lower, yellow_upper)
        
        # 先保存原始截图
        cv2.imwrite("test_screen.png", screen)
        print("已保存原始截图: test_screen.png")
        
        doors = []
        has_valid_doors = False

        # 处理蓝灯
        print("\n=== 蓝灯检测 ===")
        contours, _ = cv2.findContours(blue_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"找到 {len(contours)} 个蓝色轮廓")

        blue_doors_found = 0
        
        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            print(f"轮廓 {i}: 面积 = {area}")
            
            if area > 50:
                x, y, w_rect, h_rect = cv2.boundingRect(contour)
                print(f"  边界框: x={x}, y={y}, w={w_rect}, h={h_rect}")
                
                if x > w * 0.4:  # 在右半部分
                    blue_doors_found += 1
                    door_x = x + w_rect // 2
                    door_y = y + h_rect // 2
                    print(f"  ✓ 蓝灯门 {blue_doors_found}: 中心坐标=({door_x}, {door_y})")
                    doors.append((door_x, door_y, "蓝灯", area))
                    has_valid_doors = True

                    # 在原图上标记
                    cv2.rectangle(screen, (x, y), (x + w_rect, y + h_rect), (255, 0, 0), 2)
                    cv2.circle(screen, (door_x, door_y), 5, (0, 255, 0), -1)
                    cv2.putText(screen, f"Blue{blue_doors_found}", (door_x-20, door_y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                else:
                    print(f"  ✗ 位置太左，跳过")
        
        # 处理黄灯
        print("\n=== 黄灯检测 ===")
        contours, _ = cv2.findContours(yellow_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"找到 {len(contours)} 个黄色轮廓")

        yellow_doors_found = 0
        
        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            print(f"轮廓 {i}: 面积 = {area}")
            
            if area > 50:
                x, y, w_rect, h_rect = cv2.boundingRect(contour)
                print(f"  边界框: x={x}, y={y}, w={w_rect}, h={h_rect}")
                
                if x > w * 0.4:  # 在右半部分
                    yellow_doors_found += 1
                    door_x = x + w_rect // 2
                    door_y = y + h_rect // 2
                    print(f"  ✓ 黄灯门 {yellow_doors_found}: 中心坐标=({door_x}, {door_y})")
                    doors.append((door_x, door_y, "黄灯", area))
                    has_valid_doors = True

                    # 在原图上标记
                    cv2.rectangle(screen, (x, y), (x + w_rect, y + h_rect), (0, 255, 255), 2)
                    cv2.circle(screen, (door_x, door_y), 5, (0, 0, 255), -1)
                    cv2.putText(screen, f"Yellow{yellow_doors_found}", (door_x-30, door_y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
                else:
                    print(f"  ✗ 位置太左，跳过")
        
        # 只有找到门时才保存调试图像
        if has_valid_doors:
            cv2.imwrite("test_blue_mask.png", blue_mask)
            cv2.imwrite("test_yellow_mask.png", yellow_mask)
            cv2.imwrite("test_marked.png", screen)
            print(f"\n已保存调试图像:")
            print("- test_blue_mask.png: 蓝色检测结果")
            print("- test_yellow_mask.png: 黄色检测结果")
            print("- test_marked.png: 标记了所有门的图像")

        # 总结
        print(f"\n=== 检测结果 ===")
        if doors:
            print(f"总共找到 {len(doors)} 个门:")
            for i, (x, y, door_type, area) in enumerate(doors):
                print(f"  {i+1}. {door_type}: 坐标=({x}, {y}), 面积={area}")
        else:
            print("未检测到任何门灯")
            print("可能原因:")
            print("1. 当前画面没有门")
            print("2. 门灯颜色不在检测范围内")
            print("3. 门在画面左侧（被过滤掉了）")

        return doors

def main():
    """主函数"""
    
    print("坐标计算测试工具")
    print("=" * 30)
    
    tester = CoordinateTest()
    
    if not tester.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    print("请确保角色在地下城房间中")
    input("准备好后按回车开始测试...")
    
    doors = tester.test_door_coordinates()
    
    if doors:
        print("\n✓ 检测成功！")
        print("请查看生成的调试文件来验证门的位置")
        print("\n每个门的坐标应该都不同！")
        print("如果坐标相同，说明计算有问题")
    else:
        print("\n✗ 未检测到任何门")
        print("只保存了原始截图: test_screen.png")
        print("请确保:")
        print("1. 角色在地下城房间中")
        print("2. 画面右侧有蓝灯或黄灯的门")
        print("3. 门灯足够亮且清晰可见")

if __name__ == "__main__":
    main()
