#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SendInput API测试
使用Windows SendInput API进行底层键盘模拟
"""

import ctypes
import time
from ctypes import wintypes, Structure, Union, POINTER, byref

# Windows API常量
INPUT_MOUSE = 0
INPUT_KEYBOARD = 1
INPUT_HARDWARE = 2

KEYEVENTF_EXTENDEDKEY = 0x0001
KEYEVENTF_KEYUP = 0x0002
KEYEVENTF_UNICODE = 0x0004
KEYEVENTF_SCANCODE = 0x0008

MAPVK_VK_TO_VSC = 0

# 定义结构体
class MOUSEINPUT(Structure):
    _fields_ = [
        ("dx", ctypes.c_long),
        ("dy", ctypes.c_long),
        ("mouseData", wintypes.DWORD),
        ("dwFlags", wintypes.DWORD),
        ("time", wintypes.DWORD),
        ("dwExtraInfo", POINTER(wintypes.ULONG))
    ]

class KEYBDINPUT(Structure):
    _fields_ = [
        ("wVk", wintypes.WORD),
        ("wScan", wintypes.WORD),
        ("dwFlags", wintypes.DWORD),
        ("time", wintypes.DWORD),
        ("dwExtraInfo", POINTER(wintypes.ULONG))
    ]

class HARDWAREINPUT(Structure):
    _fields_ = [
        ("uMsg", wintypes.DWORD),
        ("wParamL", wintypes.WORD),
        ("wParamH", wintypes.WORD)
    ]

class INPUT_UNION(Union):
    _fields_ = [
        ("mi", MOUSEINPUT),
        ("ki", KEYBDINPUT),
        ("hi", HARDWAREINPUT)
    ]

class INPUT(Structure):
    _fields_ = [
        ("type", wintypes.DWORD),
        ("ii", INPUT_UNION)
    ]

# 虚拟键码
VK_CODES = {
    'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46,
    'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A, 'k': 0x4B, 'l': 0x4C,
    'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
    's': 0x53, 't': 0x54, 'u': 0x16, 'v': 0x56, 'w': 0x57, 'x': 0x58,
    'y': 0x59, 'z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
    '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
    'space': 0x20, 'enter': 0x0D, 'esc': 0x1B, 'tab': 0x09,
    'shift': 0x10, 'ctrl': 0x11, 'alt': 0x12,
    'left': 0x25, 'up': 0x26, 'right': 0x27, 'down': 0x28,
    'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74,
    'f6': 0x75, 'f7': 0x76, 'f8': 0x77, 'f9': 0x78, 'f10': 0x79,
    'f11': 0x7A, 'f12': 0x7B
}

class SendInputTester:
    """SendInput测试器"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.game_hwnd = None
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or 
                        "Fighter" in window_title or
                        "地下城与勇士" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title} (句柄: {hwnd})")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def activate_window(self):
        """激活窗口"""
        if self.game_hwnd:
            # 多种方式激活窗口
            self.user32.ShowWindow(self.game_hwnd, 9)  # SW_RESTORE
            self.user32.SetForegroundWindow(self.game_hwnd)
            self.user32.BringWindowToTop(self.game_hwnd)
            
            # 等待窗口激活
            time.sleep(0.3)
            
            # 验证窗口是否激活
            active_hwnd = self.user32.GetForegroundWindow()
            if active_hwnd == self.game_hwnd:
                print("✓ 窗口激活成功")
                return True
            else:
                print("⚠ 窗口可能未完全激活")
                return True  # 仍然尝试发送输入
        return False
    
    def send_key_basic(self, key):
        """方法1: 基础SendInput"""
        vk_code = VK_CODES.get(key.lower())
        if not vk_code:
            print(f"未知按键: {key}")
            return False
        
        print(f"基础SendInput: {key} (VK: 0x{vk_code:02X})")
        
        # 激活窗口
        self.activate_window()
        
        # 创建输入结构
        inputs = (INPUT * 2)()
        
        # 按下事件
        inputs[0].type = INPUT_KEYBOARD
        inputs[0].ii.ki.wVk = vk_code
        inputs[0].ii.ki.wScan = 0
        inputs[0].ii.ki.dwFlags = 0
        inputs[0].ii.ki.time = 0
        inputs[0].ii.ki.dwExtraInfo = None
        
        # 释放事件
        inputs[1].type = INPUT_KEYBOARD
        inputs[1].ii.ki.wVk = vk_code
        inputs[1].ii.ki.wScan = 0
        inputs[1].ii.ki.dwFlags = KEYEVENTF_KEYUP
        inputs[1].ii.ki.time = 0
        inputs[1].ii.ki.dwExtraInfo = None
        
        # 发送输入
        result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
        print(f"SendInput结果: {result}/2")
        
        return result == 2
    
    def send_key_scancode(self, key):
        """方法2: 使用扫描码的SendInput"""
        vk_code = VK_CODES.get(key.lower())
        if not vk_code:
            return False
        
        # 获取扫描码
        scan_code = self.user32.MapVirtualKeyW(vk_code, MAPVK_VK_TO_VSC)
        
        print(f"扫描码SendInput: {key} (VK: 0x{vk_code:02X}, SC: 0x{scan_code:02X})")
        
        # 激活窗口
        self.activate_window()
        
        # 创建输入结构
        inputs = (INPUT * 2)()
        
        # 按下事件
        inputs[0].type = INPUT_KEYBOARD
        inputs[0].ii.ki.wVk = 0  # 使用扫描码时VK设为0
        inputs[0].ii.ki.wScan = scan_code
        inputs[0].ii.ki.dwFlags = KEYEVENTF_SCANCODE
        inputs[0].ii.ki.time = 0
        inputs[0].ii.ki.dwExtraInfo = None
        
        # 释放事件
        inputs[1].type = INPUT_KEYBOARD
        inputs[1].ii.ki.wVk = 0
        inputs[1].ii.ki.wScan = scan_code
        inputs[1].ii.ki.dwFlags = KEYEVENTF_SCANCODE | KEYEVENTF_KEYUP
        inputs[1].ii.ki.time = 0
        inputs[1].ii.ki.dwExtraInfo = None
        
        # 发送输入
        result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
        print(f"SendInput结果: {result}/2")
        
        return result == 2
    
    def send_key_extended(self, key):
        """方法3: 扩展键SendInput（用于方向键等）"""
        vk_code = VK_CODES.get(key.lower())
        if not vk_code:
            return False
        
        print(f"扩展键SendInput: {key}")
        
        # 激活窗口
        self.activate_window()
        
        # 判断是否为扩展键
        extended_keys = ['left', 'right', 'up', 'down', 'ctrl', 'alt']
        is_extended = key.lower() in extended_keys
        
        # 创建输入结构
        inputs = (INPUT * 2)()
        
        # 按下事件
        inputs[0].type = INPUT_KEYBOARD
        inputs[0].ii.ki.wVk = vk_code
        inputs[0].ii.ki.wScan = self.user32.MapVirtualKeyW(vk_code, MAPVK_VK_TO_VSC)
        inputs[0].ii.ki.dwFlags = KEYEVENTF_EXTENDEDKEY if is_extended else 0
        inputs[0].ii.ki.time = 0
        inputs[0].ii.ki.dwExtraInfo = None
        
        # 释放事件
        inputs[1].type = INPUT_KEYBOARD
        inputs[1].ii.ki.wVk = vk_code
        inputs[1].ii.ki.wScan = self.user32.MapVirtualKeyW(vk_code, MAPVK_VK_TO_VSC)
        inputs[1].ii.ki.dwFlags = (KEYEVENTF_EXTENDEDKEY if is_extended else 0) | KEYEVENTF_KEYUP
        inputs[1].ii.ki.time = 0
        inputs[1].ii.ki.dwExtraInfo = None
        
        # 发送输入
        result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
        print(f"SendInput结果: {result}/2")
        
        return result == 2
    
    def send_key_with_delay(self, key, press_duration=0.05):
        """方法4: 带延迟的SendInput"""
        vk_code = VK_CODES.get(key.lower())
        if not vk_code:
            return False
        
        print(f"延迟SendInput: {key} (延迟: {press_duration}s)")
        
        # 激活窗口
        self.activate_window()
        
        # 按下
        input_down = INPUT()
        input_down.type = INPUT_KEYBOARD
        input_down.ii.ki.wVk = vk_code
        input_down.ii.ki.wScan = 0
        input_down.ii.ki.dwFlags = 0
        input_down.ii.ki.time = 0
        input_down.ii.ki.dwExtraInfo = None
        
        result1 = self.user32.SendInput(1, byref(input_down), ctypes.sizeof(INPUT))
        
        # 等待
        time.sleep(press_duration)
        
        # 释放
        input_up = INPUT()
        input_up.type = INPUT_KEYBOARD
        input_up.ii.ki.wVk = vk_code
        input_up.ii.ki.wScan = 0
        input_up.ii.ki.dwFlags = KEYEVENTF_KEYUP
        input_up.ii.ki.time = 0
        input_up.ii.ki.dwExtraInfo = None
        
        result2 = self.user32.SendInput(1, byref(input_up), ctypes.sizeof(INPUT))
        
        print(f"SendInput结果: 按下={result1}, 释放={result2}")
        
        return result1 == 1 and result2 == 1
    
    def send_combination(self, keys):
        """发送组合键"""
        vk_codes = []
        for key in keys:
            vk_code = VK_CODES.get(key.lower())
            if vk_code:
                vk_codes.append(vk_code)
            else:
                print(f"未知按键: {key}")
                return False
        
        print(f"组合键SendInput: {'+'.join(keys)}")
        
        # 激活窗口
        self.activate_window()
        
        # 创建输入数组
        input_count = len(vk_codes) * 2  # 每个键需要按下和释放
        inputs = (INPUT * input_count)()
        
        # 按下所有键
        for i, vk_code in enumerate(vk_codes):
            inputs[i].type = INPUT_KEYBOARD
            inputs[i].ii.ki.wVk = vk_code
            inputs[i].ii.ki.wScan = 0
            inputs[i].ii.ki.dwFlags = 0
            inputs[i].ii.ki.time = 0
            inputs[i].ii.ki.dwExtraInfo = None
        
        # 释放所有键（逆序）
        for i, vk_code in enumerate(reversed(vk_codes)):
            idx = len(vk_codes) + i
            inputs[idx].type = INPUT_KEYBOARD
            inputs[idx].ii.ki.wVk = vk_code
            inputs[idx].ii.ki.wScan = 0
            inputs[idx].ii.ki.dwFlags = KEYEVENTF_KEYUP
            inputs[idx].ii.ki.time = 0
            inputs[idx].ii.ki.dwExtraInfo = None
        
        # 发送输入
        result = self.user32.SendInput(input_count, inputs, ctypes.sizeof(INPUT))
        print(f"SendInput结果: {result}/{input_count}")
        
        return result == input_count

def main():
    """主函数"""
    print("SendInput API测试工具")
    print("=" * 40)
    
    tester = SendInputTester()
    
    if not tester.find_dnf_window():
        print("错误: 未找到DNF窗口")
        print("请确保DNF游戏已启动")
        return
    
    print("\n请确保:")
    print("1. DNF角色在可以移动的状态")
    print("2. 没有打开对话框或聊天框")
    print("3. 角色可以自由移动")
    print()
    
    while True:
        print("\n选择SendInput测试方法:")
        print("1. 基础SendInput (虚拟键码)")
        print("2. 扫描码SendInput")
        print("3. 扩展键SendInput (方向键优化)")
        print("4. 带延迟SendInput")
        print("5. 组合键测试")
        print("6. 测试所有方法")
        print("7. 连续移动测试")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-7): ").strip()
        
        if choice == '0':
            break
        elif choice in ['1', '2', '3', '4']:
            key = input("请输入要测试的按键 (如: right, e, space): ").strip()
            if not key:
                continue
            
            print(f"\n测试按键: {key}")
            print("请观察游戏中角色是否有反应...")
            
            if choice == '1':
                tester.send_key_basic(key)
            elif choice == '2':
                tester.send_key_scancode(key)
            elif choice == '3':
                tester.send_key_extended(key)
            elif choice == '4':
                duration = input("请输入按键持续时间(秒，默认0.05): ").strip()
                try:
                    duration = float(duration) if duration else 0.05
                except:
                    duration = 0.05
                tester.send_key_with_delay(key, duration)
                
        elif choice == '5':
            keys_str = input("请输入组合键 (如: alt+right): ").strip()
            if keys_str:
                keys = [k.strip() for k in keys_str.split('+')]
                tester.send_combination(keys)
                
        elif choice == '6':
            key = input("请输入要测试的按键 (如: right): ").strip()
            if not key:
                continue
            
            print(f"\n测试所有SendInput方法，按键: {key}")
            
            methods = [
                ("基础SendInput", lambda k: tester.send_key_basic(k)),
                ("扫描码SendInput", lambda k: tester.send_key_scancode(k)),
                ("扩展键SendInput", lambda k: tester.send_key_extended(k)),
                ("带延迟SendInput", lambda k: tester.send_key_with_delay(k, 0.1))
            ]
            
            for name, method in methods:
                print(f"\n正在测试: {name}")
                method(key)
                time.sleep(2)  # 等待观察效果
                
        elif choice == '7':
            print("\n连续移动测试 - 角色应该向右移动5次")
            for i in range(5):
                print(f"移动 {i+1}/5")
                tester.send_key_extended('right')
                time.sleep(0.5)
            
        else:
            print("无效选择")
    
    print("测试结束")

if __name__ == "__main__":
    main()
