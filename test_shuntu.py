#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF顺图专项测试
测试各种可能的顺图方法
"""

import ctypes
import time
from ctypes import wintypes, byref

# 扫描码
SCAN_CODES = {
    'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0,
    'alt': 0x38, 'e': 0x12, 'space': 0x39, 'enter': 0x1C,
    'ctrl': 0x1D, 'shift': 0x2A, 'tab': 0x0F,
}

class ShuntuTester:
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.enable_debug_privilege()
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = ctypes.windll.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                print("✓ 调试权限已启用")
        except Exception as e:
            print(f"调试权限启用失败: {e}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def send_key(self, key):
        """发送单个按键"""
        scan_code = SCAN_CODES.get(key.lower())
        if not scan_code:
            return False
        
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            self.user32.keybd_event(0, scan_code, 0x0008, 0)
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
            return True
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False
    
    def method1_standard_combo(self, direction):
        """方法1: 标准组合键"""
        print(f"方法1: 标准 Alt+{direction}")
        
        alt_code = SCAN_CODES['alt']
        dir_code = SCAN_CODES[direction]
        
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 同时按下
            self.user32.keybd_event(0, alt_code, 0x0008, 0)
            time.sleep(0.01)
            self.user32.keybd_event(0, dir_code, 0x0008, 0)
            
            time.sleep(0.1)  # 保持
            
            # 同时释放
            self.user32.keybd_event(0, dir_code, 0x0008 | 0x0002, 0)
            time.sleep(0.01)
            self.user32.keybd_event(0, alt_code, 0x0008 | 0x0002, 0)
            
            return True
        except Exception as e:
            print(f"方法1失败: {e}")
            return False
    
    def method2_sequential(self, direction):
        """方法2: 先按Alt再按方向键"""
        print(f"方法2: 先Alt后{direction}")
        
        alt_code = SCAN_CODES['alt']
        dir_code = SCAN_CODES[direction]
        
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 先按Alt
            self.user32.keybd_event(0, alt_code, 0x0008, 0)
            time.sleep(0.05)
            
            # 再按方向键
            self.user32.keybd_event(0, dir_code, 0x0008, 0)
            time.sleep(0.05)
            self.user32.keybd_event(0, dir_code, 0x0008 | 0x0002, 0)
            
            # 释放Alt
            self.user32.keybd_event(0, alt_code, 0x0008 | 0x0002, 0)
            
            return True
        except Exception as e:
            print(f"方法2失败: {e}")
            return False
    
    def method3_long_press(self, direction):
        """方法3: 长按组合键"""
        print(f"方法3: 长按 Alt+{direction}")
        
        alt_code = SCAN_CODES['alt']
        dir_code = SCAN_CODES[direction]
        
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 按下
            self.user32.keybd_event(0, alt_code, 0x0008, 0)
            time.sleep(0.02)
            self.user32.keybd_event(0, dir_code, 0x0008, 0)
            
            time.sleep(0.5)  # 长按
            
            # 释放
            self.user32.keybd_event(0, dir_code, 0x0008 | 0x0002, 0)
            time.sleep(0.02)
            self.user32.keybd_event(0, alt_code, 0x0008 | 0x0002, 0)
            
            return True
        except Exception as e:
            print(f"方法3失败: {e}")
            return False
    
    def method4_multiple_press(self, direction):
        """方法4: 多次快速按压"""
        print(f"方法4: 多次 Alt+{direction}")
        
        for i in range(3):
            print(f"  第{i+1}次...")
            self.method1_standard_combo(direction)
            time.sleep(0.2)
        
        return True
    
    def method5_other_keys(self, direction):
        """方法5: 尝试其他可能的组合键"""
        print(f"方法5: 其他组合键")
        
        # 尝试Ctrl+方向键
        print(f"  尝试 Ctrl+{direction}")
        ctrl_code = SCAN_CODES['ctrl']
        dir_code = SCAN_CODES[direction]
        
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            self.user32.keybd_event(0, ctrl_code, 0x0008, 0)
            time.sleep(0.01)
            self.user32.keybd_event(0, dir_code, 0x0008, 0)
            time.sleep(0.1)
            self.user32.keybd_event(0, dir_code, 0x0008 | 0x0002, 0)
            time.sleep(0.01)
            self.user32.keybd_event(0, ctrl_code, 0x0008 | 0x0002, 0)
            
            time.sleep(1)
            
            # 尝试Shift+方向键
            print(f"  尝试 Shift+{direction}")
            shift_code = SCAN_CODES['shift']
            
            self.user32.keybd_event(0, shift_code, 0x0008, 0)
            time.sleep(0.01)
            self.user32.keybd_event(0, dir_code, 0x0008, 0)
            time.sleep(0.1)
            self.user32.keybd_event(0, dir_code, 0x0008 | 0x0002, 0)
            time.sleep(0.01)
            self.user32.keybd_event(0, shift_code, 0x0008 | 0x0002, 0)
            
            return True
        except Exception as e:
            print(f"方法5失败: {e}")
            return False
    
    def method6_space_direction(self, direction):
        """方法6: 空格+方向键"""
        print(f"方法6: Space+{direction}")
        
        space_code = SCAN_CODES['space']
        dir_code = SCAN_CODES[direction]
        
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            self.user32.keybd_event(0, space_code, 0x0008, 0)
            time.sleep(0.01)
            self.user32.keybd_event(0, dir_code, 0x0008, 0)
            time.sleep(0.1)
            self.user32.keybd_event(0, dir_code, 0x0008 | 0x0002, 0)
            time.sleep(0.01)
            self.user32.keybd_event(0, space_code, 0x0008 | 0x0002, 0)
            
            return True
        except Exception as e:
            print(f"方法6失败: {e}")
            return False
    
    def test_all_shuntu_methods(self, direction='right'):
        """测试所有顺图方法"""
        print(f"\n=== 测试所有顺图方法 - 方向: {direction} ===")
        print("请确保:")
        print("1. 角色在地下城中")
        print("2. 当前房间已清空")
        print("3. 可以看到下个房间的门")
        print()
        
        methods = [
            ("方法1: 标准组合键", self.method1_standard_combo),
            ("方法2: 先Alt后方向", self.method2_sequential),
            ("方法3: 长按组合键", self.method3_long_press),
            ("方法4: 多次按压", self.method4_multiple_press),
            ("方法5: 其他组合键", self.method5_other_keys),
            ("方法6: 空格+方向", self.method6_space_direction),
        ]
        
        for name, method in methods:
            print(f"\n{name}")
            input("按回车开始测试...")
            
            try:
                result = method(direction)
                print(f"发送结果: {'成功' if result else '失败'}")
                
                response = input("是否成功顺图? (y/n/s=跳过): ").strip().lower()
                if response == 'y':
                    print(f"✓ {name} 有效!")
                    return name, method
                elif response == 's':
                    print("跳过此方法")
                    continue
                else:
                    print(f"✗ {name} 无效")
                    
                time.sleep(1)
            except Exception as e:
                print(f"异常: {e}")
        
        print("\n所有方法都无效")
        return None, None
    
    def manual_test(self):
        """手动测试"""
        while True:
            print("\n" + "=" * 40)
            print("DNF顺图专项测试")
            print("=" * 40)
            print("1. 测试所有方法 (右)")
            print("2. 测试所有方法 (下)")
            print("3. 测试所有方法 (上)")
            print("4. 测试所有方法 (左)")
            print("5. 单独测试标准组合键")
            print("6. 单独测试先Alt后方向")
            print("7. 只测试单个方向键")
            print("0. 退出")
            
            choice = input("\n请选择: ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                self.test_all_shuntu_methods('right')
            elif choice == '2':
                self.test_all_shuntu_methods('down')
            elif choice == '3':
                self.test_all_shuntu_methods('up')
            elif choice == '4':
                self.test_all_shuntu_methods('left')
            elif choice == '5':
                direction = input("请输入方向 (right/down/up/left): ").strip()
                if direction in SCAN_CODES:
                    self.method1_standard_combo(direction)
                    input("观察结果后按回车...")
            elif choice == '6':
                direction = input("请输入方向 (right/down/up/left): ").strip()
                if direction in SCAN_CODES:
                    self.method2_sequential(direction)
                    input("观察结果后按回车...")
            elif choice == '7':
                direction = input("请输入方向 (right/down/up/left): ").strip()
                if direction in SCAN_CODES:
                    print(f"只按{direction}键...")
                    self.send_key(direction)
                    input("观察结果后按回车...")
            else:
                print("无效选择")

def main():
    """主函数"""
    print("DNF顺图专项测试工具")
    print("=" * 30)
    print("测试各种可能的顺图按键组合")
    
    tester = ShuntuTester()
    
    if not tester.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    print("\n重要提示:")
    print("1. 请先进入地下城")
    print("2. 清空当前房间")
    print("3. 确保可以看到下个房间的门")
    print("4. 观察每次测试后角色是否移动到下个房间")
    
    tester.manual_test()

if __name__ == "__main__":
    main()
