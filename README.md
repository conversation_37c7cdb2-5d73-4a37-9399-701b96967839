# DNF格兰迪搬砖自动化脚本

这是一个用于DNF（地下城与勇士）格兰迪搬砖的自动化脚本，支持自动打怪、翻牌、背包管理等功能。

## 功能特性

### 核心功能
- ✅ 自动进入格兰迪地下城（地狱难度）
- ✅ 自动打怪和清理房间
- ✅ 自动翻牌和返回城镇
- ✅ 智能背包管理和装备分解
- ✅ 可配置的工作时长控制

### 快捷键功能
- `Alt+Q`: 开启/关闭自动入包
- `Alt+W`: 快速修理穿戴装备
- `Alt+D`: 直接分解装备
- `Alt+Right`: 顺图到下一房间
- `0`: 摆摊分解机
- `I`: 打开背包
- `4`: 数字键4功能

### 智能检测
- 游戏状态检测（城镇/地下城/地图选择/翻牌）
- 背包满度检测和自动处理
- 小地图识别和位置判断
- 界面元素自动识别

## 安装和配置

### 1. 环境要求
- Windows 10/11
- Python 3.8+
- DNF游戏客户端

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置设置
编辑 `config.ini` 文件：

```ini
[GENERAL]
# 搬砖时长（小时）
work_hours = 8
# 技能释放键
skill_key = e

[FEATURES]
# 是否摆摊分解
use_stall_decompose = true
# 是否刷深渊
enable_abyss = false

[DETECTION]
# 背包满度阈值（百分比）
bag_full_threshold = 80
```

### 4. 图像资源
在 `images/` 目录下放置以下模板图像：
- `town_map.png` - 城镇小地图
- `grandi.png` - 格兰迪选项
- `hell_difficulty.png` - 地狱难度选项
- `flip_card.png` - 翻牌界面
- `return_town.png` - 返回城镇按钮
- 其他界面元素图像...

## 使用方法

### 1. 启动脚本
```bash
python main.py
```

### 2. 游戏准备
- 确保DNF游戏已启动并登录
- 角色位于城镇中
- 确保有足够的疲劳值

### 3. 运行监控
- 脚本会自动检测游戏状态
- 日志文件保存在 `logs/` 目录
- 可通过快捷键手动控制

## 工作流程

1. **状态检测**: 检测当前是否在城镇
2. **进入地图**: 选择格兰迪地下城（地狱难度）
3. **自动打怪**: 使用配置的技能键清理房间
4. **顺图移动**: 自动移动到下一房间
5. **翻牌处理**: 完成后自动翻牌
6. **返回城镇**: 自动返回城镇
7. **背包管理**: 检查背包，必要时分解装备
8. **循环执行**: 重复以上流程直到达到设定时长

## 注意事项

### 安全使用
- 本脚本仅供学习和研究使用
- 使用前请了解游戏相关规则
- 建议在测试环境中先行验证

### 技术限制
- 需要准确的模板图像进行识别
- 依赖游戏界面的稳定性
- 可能需要根据不同分辨率调整参数

### 故障排除
- 检查游戏窗口是否可见
- 确认模板图像是否正确
- 查看日志文件获取详细错误信息

## 配置说明

### 主要配置项
- `work_hours`: 自动运行时长
- `skill_key`: 主要攻击技能键
- `use_stall_decompose`: 是否使用摆摊分解
- `enable_abyss`: 是否开启深渊模式
- `bag_full_threshold`: 背包满度阈值

### 检测参数
- `image_threshold`: 图像识别相似度阈值
- `detection_interval`: 状态检测间隔
- `images_path`: 模板图像路径

## 开发说明

### 项目结构
```
dnf/
├── main.py              # 主程序入口
├── config.ini           # 配置文件
├── requirements.txt     # 依赖包列表
├── modules/            # 核心模块
│   ├── config_manager.py    # 配置管理
│   ├── input_simulator.py   # 键鼠模拟
│   ├── image_detector.py    # 图像识别
│   ├── game_controller.py   # 游戏控制
│   ├── game_state.py        # 状态检测
│   ├── inventory_manager.py # 背包管理
│   ├── hotkey_manager.py    # 快捷键管理
│   └── logger.py           # 日志系统
├── images/             # 模板图像
└── logs/              # 日志文件
```

### 扩展开发
- 可以添加更多的游戏状态检测
- 支持更多的快捷键功能
- 优化图像识别算法
- 添加更多的安全检查

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基本的搬砖功能
- 支持快捷键操作
- 添加配置文件系统

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和游戏规则。
