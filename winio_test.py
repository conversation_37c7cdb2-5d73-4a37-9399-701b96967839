#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WinIO驱动测试
使用WinIO驱动进行底层键盘模拟
"""

import ctypes
import time
import os
import sys

# WinIO相关常量
KBC_KEY_CMD = 0x64
KBC_KEY_DATA = 0x60

class WinIOKeyboard:
    """WinIO键盘模拟器"""
    
    def __init__(self):
        self.winio = None
        self.initialized = False
        self.load_winio()
    
    def load_winio(self):
        """加载WinIO驱动"""
        try:
            # 尝试加载WinIO.dll
            winio_paths = [
                "WinIO.dll",
                "WinIO32.dll", 
                "WinIO64.dll",
                os.path.join(os.path.dirname(__file__), "WinIO.dll"),
                os.path.join(os.path.dirname(__file__), "WinIO32.dll"),
                os.path.join(os.path.dirname(__file__), "WinIO64.dll")
            ]
            
            for path in winio_paths:
                if os.path.exists(path):
                    try:
                        self.winio = ctypes.windll.LoadLibrary(path)
                        print(f"成功加载WinIO: {path}")
                        break
                    except Exception as e:
                        print(f"加载 {path} 失败: {e}")
                        continue
            
            if not self.winio:
                print("未找到WinIO.dll文件")
                return False
            
            # 初始化WinIO
            if self.winio.InitializeWinIo():
                self.initialized = True
                print("WinIO初始化成功")
                return True
            else:
                print("WinIO初始化失败")
                return False
                
        except Exception as e:
            print(f"WinIO加载异常: {e}")
            return False
    
    def cleanup(self):
        """清理WinIO"""
        if self.initialized and self.winio:
            self.winio.ShutdownWinIo()
            self.initialized = False
            print("WinIO已清理")
    
    def wait_for_keyboard_buffer(self):
        """等待键盘缓冲区准备就绪"""
        timeout = 1000
        while timeout > 0:
            status = ctypes.c_ubyte()
            if self.winio.GetPortVal(KBC_KEY_CMD, ctypes.byref(status), 1):
                if not (status.value & 0x02):  # 输入缓冲区为空
                    return True
            time.sleep(0.001)
            timeout -= 1
        return False
    
    def send_keyboard_command(self, cmd):
        """发送键盘命令"""
        if not self.wait_for_keyboard_buffer():
            return False
        
        cmd_byte = ctypes.c_ubyte(cmd)
        return self.winio.SetPortVal(KBC_KEY_CMD, cmd_byte, 1)
    
    def send_keyboard_data(self, data):
        """发送键盘数据"""
        if not self.wait_for_keyboard_buffer():
            return False
        
        data_byte = ctypes.c_ubyte(data)
        return self.winio.SetPortVal(KBC_KEY_DATA, data_byte, 1)
    
    def send_key_winio(self, scan_code, press=True):
        """使用WinIO发送按键"""
        if not self.initialized:
            print("WinIO未初始化")
            return False
        
        try:
            # 发送扫描码
            if press:
                # 按下
                result = self.send_keyboard_data(scan_code)
            else:
                # 释放 (扫描码 | 0x80)
                result = self.send_keyboard_data(scan_code | 0x80)
            
            return result
        except Exception as e:
            print(f"WinIO发送按键失败: {e}")
            return False
    
    def press_key_winio(self, scan_code, duration=0.05):
        """WinIO按键（按下+释放）"""
        if not self.initialized:
            return False
        
        # 按下
        if not self.send_key_winio(scan_code, True):
            return False
        
        time.sleep(duration)
        
        # 释放
        if not self.send_key_winio(scan_code, False):
            return False
        
        return True

# 扫描码映射
SCAN_CODES = {
    'a': 0x1E, 'b': 0x30, 'c': 0x2E, 'd': 0x20, 'e': 0x12, 'f': 0x21,
    'g': 0x22, 'h': 0x23, 'i': 0x17, 'j': 0x24, 'k': 0x25, 'l': 0x26,
    'm': 0x32, 'n': 0x31, 'o': 0x18, 'p': 0x19, 'q': 0x10, 'r': 0x13,
    's': 0x1F, 't': 0x14, 'u': 0x16, 'v': 0x2F, 'w': 0x11, 'x': 0x2D,
    'y': 0x15, 'z': 0x2C,
    '0': 0x0B, '1': 0x02, '2': 0x03, '3': 0x04, '4': 0x05,
    '5': 0x06, '6': 0x07, '7': 0x08, '8': 0x09, '9': 0x0A,
    'space': 0x39, 'enter': 0x1C, 'esc': 0x01, 'tab': 0x0F,
    'shift': 0x2A, 'ctrl': 0x1D, 'alt': 0x38,
    'left': 0x4B, 'up': 0x48, 'right': 0x4D, 'down': 0x50,
}

def create_winio_dll_info():
    """创建WinIO.dll获取说明"""
    info = """
WinIO.dll 获取说明：

WinIO是一个第三方的底层硬件访问驱动，需要单独下载。

获取方法：
1. 访问官方网站：http://www.internals.com/
2. 下载WinIO库
3. 将WinIO.dll (32位) 或 WinIO64.dll (64位) 放在脚本同目录下

注意事项：
- 需要管理员权限运行
- 某些杀毒软件可能会报警（因为是底层驱动）
- 在Windows 10/11上可能需要禁用驱动签名验证

替代方案：
如果无法获取WinIO，可以尝试其他方法：
1. 使用专门的游戏自动化工具
2. 使用AHK (AutoHotkey) 脚本
3. 寻找其他底层输入库
"""
    return info

def test_winio():
    """测试WinIO功能"""
    print("WinIO键盘模拟测试")
    print("=" * 30)
    
    keyboard = WinIOKeyboard()
    
    if not keyboard.initialized:
        print("WinIO初始化失败！")
        print()
        print(create_winio_dll_info())
        return
    
    try:
        while True:
            print("\n请选择测试:")
            print("1. 测试字母键 (e)")
            print("2. 测试方向键 (right)")
            print("3. 测试空格键")
            print("4. 测试ESC键")
            print("5. 自定义按键")
            print("0. 退出")
            
            choice = input("\n请输入选择: ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                print("发送字母键 'e'...")
                if keyboard.press_key_winio(SCAN_CODES['e']):
                    print("✓ 发送成功")
                else:
                    print("✗ 发送失败")
            elif choice == '2':
                print("发送方向键 'right'...")
                if keyboard.press_key_winio(SCAN_CODES['right']):
                    print("✓ 发送成功")
                else:
                    print("✗ 发送失败")
            elif choice == '3':
                print("发送空格键...")
                if keyboard.press_key_winio(SCAN_CODES['space']):
                    print("✓ 发送成功")
                else:
                    print("✗ 发送失败")
            elif choice == '4':
                print("发送ESC键...")
                if keyboard.press_key_winio(SCAN_CODES['esc']):
                    print("✓ 发送成功")
                else:
                    print("✗ 发送失败")
            elif choice == '5':
                key = input("请输入按键名称: ").strip().lower()
                if key in SCAN_CODES:
                    print(f"发送按键 '{key}'...")
                    if keyboard.press_key_winio(SCAN_CODES[key]):
                        print("✓ 发送成功")
                    else:
                        print("✗ 发送失败")
                else:
                    print(f"未知按键: {key}")
            else:
                print("无效选择")
    
    finally:
        keyboard.cleanup()

def main():
    """主函数"""
    print("检查管理员权限...")
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            print("警告: 建议以管理员权限运行此脚本")
    except:
        print("无法检查管理员权限")
    
    test_winio()

if __name__ == "__main__":
    main()
