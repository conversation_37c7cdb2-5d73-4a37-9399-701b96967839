#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF替代输入方案
在没有WinIO的情况下尝试其他底层输入方法
"""

import ctypes
import time
import os
from ctypes import wintypes, Structure, Union, POINTER, byref

# 尝试导入pynput（如果已安装）
try:
    from pynput.keyboard import Key, Controller as KeyboardController
    from pynput.mouse import <PERSON><PERSON>, Controller as MouseController
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False

# Windows API常量
KEYEVENTF_KEYUP = 0x0002
KEYEVENTF_UNICODE = 0x0004
KEYEVENTF_SCANCODE = 0x0008

# 虚拟键码和扫描码
VK_CODES = {
    'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46,
    'g': 0x47, 'h': 0x48, 'i': 0x17, 'j': 0x4A, 'k': 0x4B, 'l': 0x4C,
    'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
    's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58,
    'y': 0x59, 'z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
    '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
    'space': 0x20, 'enter': 0x0D, 'esc': 0x1B, 'tab': 0x09,
    'shift': 0x10, 'ctrl': 0x11, 'alt': 0x12,
    'left': 0x25, 'up': 0x26, 'right': 0x27, 'down': 0x28,
}

SCAN_CODES = {
    'a': 0x1E, 'b': 0x30, 'c': 0x2E, 'd': 0x20, 'e': 0x12, 'f': 0x21,
    'g': 0x22, 'h': 0x23, 'i': 0x17, 'j': 0x24, 'k': 0x25, 'l': 0x26,
    'm': 0x32, 'n': 0x31, 'o': 0x18, 'p': 0x19, 'q': 0x10, 'r': 0x13,
    's': 0x1F, 't': 0x14, 'u': 0x16, 'v': 0x2F, 'w': 0x11, 'x': 0x2D,
    'y': 0x15, 'z': 0x2C,
    '0': 0x0B, '1': 0x02, '2': 0x03, '3': 0x04, '4': 0x05,
    '5': 0x06, '6': 0x07, '7': 0x08, '8': 0x09, '9': 0x0A,
    'space': 0x39, 'enter': 0x1C, 'esc': 0x01, 'tab': 0x0F,
    'shift': 0x2A, 'ctrl': 0x1D, 'alt': 0x38,
    'left': 0x4B, 'up': 0x48, 'right': 0x4D, 'down': 0x50,
}

class AlternativeInput:
    """替代输入方案"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.game_hwnd = None
        
        # 初始化pynput（如果可用）
        if PYNPUT_AVAILABLE:
            self.keyboard = KeyboardController()
            self.mouse = MouseController()
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or 
                        "Fighter" in window_title or
                        "地下城与勇士" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title} (句柄: {hwnd})")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def activate_window(self):
        """激活窗口"""
        if self.game_hwnd:
            # 强制激活窗口
            self.user32.ShowWindow(self.game_hwnd, 9)  # SW_RESTORE
            self.user32.SetForegroundWindow(self.game_hwnd)
            self.user32.BringWindowToTop(self.game_hwnd)
            self.user32.SetActiveWindow(self.game_hwnd)
            time.sleep(0.5)
            return True
        return False
    
    def method_pynput(self, key):
        """方法1: 使用pynput库"""
        if not PYNPUT_AVAILABLE:
            print("pynput库不可用")
            return False
        
        print(f"pynput方法: {key}")
        
        # 激活窗口
        self.activate_window()
        
        try:
            if key == 'right':
                self.keyboard.press(Key.right)
                time.sleep(0.05)
                self.keyboard.release(Key.right)
            elif key == 'left':
                self.keyboard.press(Key.left)
                time.sleep(0.05)
                self.keyboard.release(Key.left)
            elif key == 'up':
                self.keyboard.press(Key.up)
                time.sleep(0.05)
                self.keyboard.release(Key.up)
            elif key == 'down':
                self.keyboard.press(Key.down)
                time.sleep(0.05)
                self.keyboard.release(Key.down)
            elif key == 'space':
                self.keyboard.press(Key.space)
                time.sleep(0.05)
                self.keyboard.release(Key.space)
            elif key == 'esc':
                self.keyboard.press(Key.esc)
                time.sleep(0.05)
                self.keyboard.release(Key.esc)
            elif len(key) == 1:
                self.keyboard.press(key)
                time.sleep(0.05)
                self.keyboard.release(key)
            else:
                print(f"未知按键: {key}")
                return False
            
            return True
        except Exception as e:
            print(f"pynput发送失败: {e}")
            return False
    
    def method_direct_dll(self, key):
        """方法2: 直接调用user32.dll"""
        print(f"直接DLL方法: {key}")
        
        # 激活窗口
        self.activate_window()
        
        vk_code = VK_CODES.get(key.lower())
        if not vk_code:
            print(f"未知按键: {key}")
            return False
        
        try:
            # 获取当前线程ID
            current_thread = self.kernel32.GetCurrentThreadId()
            
            # 获取窗口线程ID
            window_thread = self.user32.GetWindowThreadProcessId(self.game_hwnd, None)
            
            # 附加到窗口线程
            self.user32.AttachThreadInput(current_thread, window_thread, True)
            
            # 发送按键
            self.user32.keybd_event(vk_code, 0, 0, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(vk_code, 0, KEYEVENTF_KEYUP, 0)  # 释放
            
            # 分离线程
            self.user32.AttachThreadInput(current_thread, window_thread, False)
            
            return True
        except Exception as e:
            print(f"直接DLL方法失败: {e}")
            return False
    
    def method_scan_code_global(self, key):
        """方法3: 使用扫描码的全局按键"""
        print(f"扫描码全局方法: {key}")
        
        # 激活窗口
        self.activate_window()
        
        scan_code = SCAN_CODES.get(key.lower())
        if not scan_code:
            print(f"未知按键: {key}")
            return False
        
        try:
            # 使用扫描码发送
            self.user32.keybd_event(0, scan_code, KEYEVENTF_SCANCODE, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, KEYEVENTF_SCANCODE | KEYEVENTF_KEYUP, 0)  # 释放
            
            return True
        except Exception as e:
            print(f"扫描码方法失败: {e}")
            return False
    
    def method_focus_and_send(self, key):
        """方法4: 强制聚焦后发送"""
        print(f"强制聚焦方法: {key}")
        
        vk_code = VK_CODES.get(key.lower())
        if not vk_code:
            return False
        
        try:
            # 强制聚焦
            self.user32.SetFocus(self.game_hwnd)
            self.user32.SetCapture(self.game_hwnd)
            time.sleep(0.1)
            
            # 发送按键
            self.user32.keybd_event(vk_code, 0, 0, 0)
            time.sleep(0.05)
            self.user32.keybd_event(vk_code, 0, KEYEVENTF_KEYUP, 0)
            
            # 释放捕获
            self.user32.ReleaseCapture()
            
            return True
        except Exception as e:
            print(f"强制聚焦方法失败: {e}")
            return False

def main():
    """主函数"""
    print("DNF替代输入方案测试")
    print("=" * 40)
    
    # 检查是否有pynput
    if PYNPUT_AVAILABLE:
        print("✓ pynput库可用")
    else:
        print("✗ pynput库不可用")
        print("  可以运行: pip install pynput")
    
    print()
    
    tester = AlternativeInput()
    
    if not tester.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    while True:
        print("\n选择测试方法:")
        if PYNPUT_AVAILABLE:
            print("1. pynput库方法")
        print("2. 直接DLL调用")
        print("3. 扫描码全局方法")
        print("4. 强制聚焦方法")
        print("5. 测试所有方法")
        print("0. 退出")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == '0':
            break
        elif choice in ['1', '2', '3', '4']:
            key = input("请输入要测试的按键 (如: right, e): ").strip()
            if not key:
                continue
            
            print(f"\n测试按键: {key}")
            print("请观察游戏中的反应...")
            
            if choice == '1' and PYNPUT_AVAILABLE:
                tester.method_pynput(key)
            elif choice == '2':
                tester.method_direct_dll(key)
            elif choice == '3':
                tester.method_scan_code_global(key)
            elif choice == '4':
                tester.method_focus_and_send(key)
            else:
                print("该方法不可用")
                
        elif choice == '5':
            key = input("请输入要测试的按键 (如: right): ").strip()
            if not key:
                continue
            
            print(f"\n测试所有方法，按键: {key}")
            
            methods = []
            if PYNPUT_AVAILABLE:
                methods.append(("pynput库", tester.method_pynput))
            methods.extend([
                ("直接DLL", tester.method_direct_dll),
                ("扫描码全局", tester.method_scan_code_global),
                ("强制聚焦", tester.method_focus_and_send)
            ])
            
            for name, method in methods:
                print(f"\n正在测试: {name}")
                method(key)
                time.sleep(2)
                
        else:
            print("无效选择")
    
    print("测试结束")

if __name__ == "__main__":
    main()
