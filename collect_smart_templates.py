#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF智能模板收集工具
重新设计，专注于可靠的判断方法
"""

import ctypes
import time
import os
import cv2
import numpy as np
from PIL import ImageGrab
from ctypes import wintypes, byref

class SmartTemplateCollector:
    """智能模板收集器"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.window_rect = None
        
        # 创建模板目录
        os.makedirs("templates", exist_ok=True)
        
        # 重新设计的模板列表 - 专注于可靠判断
        self.templates_needed = [
            # === 核心状态判断 ===
            ("minimap_area", "小地图区域 - 请截取整个小地图区域（用于红点检测）"),
            ("town_minimap", "城镇小地图 - 请在城镇截取小地图（用于判断是否在城镇）"),
            
            # === 地图选择 ===
            ("map_selection_title", "地图选择标题 - 请截取地图选择界面的标题文字"),
            ("grandi_text", "格兰迪文字 - 请截取格兰迪选项的文字部分"),
            ("hell_difficulty_text", "地狱难度文字 - 请截取地狱难度的文字"),
            ("enter_button", "进入按钮 - 请截取确认进入的按钮"),
            
            # === 深渊相关 ===
            ("abyss_pillar", "深渊柱子 - 请在深渊房间截取柱子图像"),
            ("abyss_complete", "深渊完成 - 请截取深渊完成时的特效或提示"),
            ("abyss_counter", "深渊计数 - 请截取显示深渊进度的区域（如果有）"),
            
            # === 房间状态 ===
            ("door_available", "可通行门 - 请截取可以通过的门（亮起状态）"),
            ("door_locked", "锁定门 - 请截取被锁定的门（暗淡状态）"),
            ("boss_room_icon", "BOSS房间图标 - 请截取小地图上BOSS房间的特殊标记"),
            
            # === 结束判断 ===
            ("dungeon_complete", "地下城完成 - 请截取地下城完成的界面"),
            ("flip_card_interface", "翻牌界面 - 请截取翻牌界面的特征"),
            ("return_town_button", "返回城镇 - 请截取返回城镇按钮"),
            
            # === 背包和修理 ===
            ("bag_full_warning", "背包满警告 - 请截取背包满的警告提示"),
            ("equipment_broken", "装备损坏 - 请截取装备损坏的红色提示"),
            ("stall_npc", "摆摊NPC - 请截取摆摊分解NPC"),
        ]
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        print(f"找到DNF窗口: {window_title}")
                        print(f"窗口大小: {rect.right-rect.left} x {rect.bottom-rect.top}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def capture_game_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.2)
            
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"截屏失败: {e}")
            return None
    
    def detect_red_dots(self, image):
        """检测红点（用于测试小地图）"""
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 定义红色范围
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 50, 50])
        upper_red2 = np.array([180, 255, 255])
        
        # 创建红色掩码
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask = mask1 + mask2
        
        # 计算红色像素数量
        red_pixels = cv2.countNonZero(mask)
        
        return red_pixels, mask
    
    def save_template_with_preview(self, name, description):
        """保存模板并提供预览功能"""
        print(f"\n=== 收集模板: {name} ===")
        print(f"说明: {description}")
        
        if name == "minimap_area":
            print("\n⚠️ 重要提示:")
            print("小地图是最关键的模板，请确保:")
            print("1. 截取完整的小地图区域")
            print("2. 包含边框，但不要包含其他界面元素")
            print("3. 这个区域将用于检测红点（怪物位置）")
        
        while True:
            choice = input("\n输入 's' 截图，'test' 测试红点检测，'skip' 跳过: ").strip().lower()
            
            if choice == 's':
                screen = self.capture_game_screen()
                if screen is not None:
                    self.select_region_and_save(screen, name, description)
                    break
                else:
                    print("截图失败，请重试")
            elif choice == 'test' and name == "minimap_area":
                self.test_red_dot_detection()
            elif choice == 'skip':
                print(f"跳过 {name}")
                break
            else:
                print("无效输入")
    
    def test_red_dot_detection(self):
        """测试红点检测"""
        print("\n=== 红点检测测试 ===")
        print("请确保当前在有怪物的房间")
        
        screen = self.capture_game_screen()
        if screen is None:
            print("截图失败")
            return
        
        # 如果已有小地图模板，使用它
        minimap_path = "templates/minimap_area.png"
        if os.path.exists(minimap_path):
            minimap_template = cv2.imread(minimap_path)
            
            # 在当前画面中找到小地图位置
            result = cv2.matchTemplate(screen, minimap_template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val > 0.7:
                h, w = minimap_template.shape[:2]
                minimap_region = screen[max_loc[1]:max_loc[1]+h, max_loc[0]:max_loc[0]+w]
                
                # 检测红点
                red_pixels, mask = self.detect_red_dots(minimap_region)
                
                print(f"检测到红色像素: {red_pixels}")
                print(f"判断: {'有怪物' if red_pixels > 50 else '无怪物'}")
                
                # 显示结果
                cv2.imshow("小地图区域", minimap_region)
                cv2.imshow("红点掩码", mask)
                cv2.waitKey(3000)
                cv2.destroyAllWindows()
            else:
                print("未找到小地图，请先收集小地图模板")
        else:
            print("请先收集小地图模板")
    
    def select_region_and_save(self, screen, name, description):
        """选择区域并保存"""
        print(f"\n在弹出窗口中选择 {name} 区域:")
        print("- 鼠标拖拽选择区域")
        print("- 空格键确认")
        print("- ESC键取消")
        
        # 创建窗口
        window_name = f"选择区域 - {name}"
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_name, 1200, 800)
        
        # 鼠标选择变量
        self.selecting = False
        self.start_point = None
        self.end_point = None
        
        def mouse_callback(event, x, y, flags, param):
            if event == cv2.EVENT_LBUTTONDOWN:
                self.selecting = True
                self.start_point = (x, y)
                self.end_point = (x, y)
            elif event == cv2.EVENT_MOUSEMOVE and self.selecting:
                self.end_point = (x, y)
                temp_screen = screen.copy()
                cv2.rectangle(temp_screen, self.start_point, self.end_point, (0, 255, 0), 2)
                cv2.imshow(window_name, temp_screen)
            elif event == cv2.EVENT_LBUTTONUP:
                self.selecting = False
        
        cv2.setMouseCallback(window_name, mouse_callback)
        cv2.imshow(window_name, screen)
        
        while True:
            key = cv2.waitKey(1) & 0xFF
            if key == ord(' '):  # 空格确认
                if self.start_point and self.end_point:
                    x1, y1 = self.start_point
                    x2, y2 = self.end_point
                    
                    x1, x2 = min(x1, x2), max(x1, x2)
                    y1, y2 = min(y1, y2), max(y1, y2)
                    
                    if x2 - x1 > 10 and y2 - y1 > 10:
                        template = screen[y1:y2, x1:x2]
                        
                        # 保存模板
                        filename = f"templates/{name}.png"
                        cv2.imwrite(filename, template)
                        print(f"✓ 已保存: {filename}")
                        print(f"  大小: {x2-x1} x {y2-y1}")
                        
                        # 如果是小地图，立即测试红点检测
                        if name == "minimap_area":
                            red_pixels, mask = self.detect_red_dots(template)
                            print(f"  红色像素: {red_pixels}")
                            
                            cv2.imshow("保存的小地图", template)
                            cv2.imshow("红点检测", mask)
                            cv2.waitKey(2000)
                        
                        cv2.destroyAllWindows()
                        break
                    else:
                        print("区域太小，请重新选择")
            elif key == 27:  # ESC取消
                cv2.destroyAllWindows()
                break
    
    def show_priority_templates(self):
        """显示优先级模板"""
        print("\n" + "=" * 60)
        print("🔥 优先收集的核心模板（按重要性排序）")
        print("=" * 60)
        
        priority_templates = [
            ("minimap_area", "小地图区域", "用于红点检测，判断是否有怪物"),
            ("town_minimap", "城镇小地图", "判断是否在城镇"),
            ("grandi_text", "格兰迪文字", "选择格兰迪地下城"),
            ("hell_difficulty_text", "地狱难度", "选择地狱难度"),
            ("abyss_pillar", "深渊柱子", "识别深渊柱子"),
            ("door_available", "可通行门", "判断是否可以顺图"),
            ("dungeon_complete", "地下城完成", "判断是否完成"),
        ]
        
        for i, (name, title, purpose) in enumerate(priority_templates, 1):
            status = "✓" if os.path.exists(f"templates/{name}.png") else "✗"
            print(f"{i}. {status} {title}")
            print(f"   用途: {purpose}")
            print(f"   文件: {name}.png")
            print()
    
    def collect_priority_templates(self):
        """收集优先模板"""
        priority_list = [
            ("minimap_area", "小地图区域 - 请截取整个小地图（最重要！）"),
            ("town_minimap", "城镇小地图 - 请在城镇截取小地图"),
            ("grandi_text", "格兰迪文字 - 请截取格兰迪选项文字"),
            ("hell_difficulty_text", "地狱难度 - 请截取地狱难度文字"),
            ("abyss_pillar", "深渊柱子 - 请在深渊房间截取柱子"),
            ("door_available", "可通行门 - 请截取亮起的门"),
            ("dungeon_complete", "地下城完成 - 请截取完成界面"),
        ]
        
        print("开始收集优先模板...")
        
        for name, description in priority_list:
            if os.path.exists(f"templates/{name}.png"):
                choice = input(f"\n{name}.png 已存在，是否重新收集? (y/n): ").strip().lower()
                if choice != 'y':
                    continue
            
            self.save_template_with_preview(name, description)

def main():
    """主函数"""
    collector = SmartTemplateCollector()
    
    if not collector.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    while True:
        print("\n" + "=" * 50)
        print("DNF智能模板收集工具")
        print("=" * 50)
        print("1. 查看优先模板列表")
        print("2. 收集优先模板")
        print("3. 收集所有模板")
        print("4. 测试红点检测")
        print("5. 查看已收集模板")
        print("0. 退出")
        
        choice = input("\n请选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            collector.show_priority_templates()
        elif choice == '2':
            collector.collect_priority_templates()
        elif choice == '3':
            for name, desc in collector.templates_needed:
                if not os.path.exists(f"templates/{name}.png"):
                    collector.save_template_with_preview(name, desc)
        elif choice == '4':
            collector.test_red_dot_detection()
        elif choice == '5':
            template_dir = "templates"
            if os.path.exists(template_dir):
                templates = [f for f in os.listdir(template_dir) if f.endswith('.png')]
                print(f"\n已收集 {len(templates)} 个模板:")
                for template in sorted(templates):
                    print(f"✓ {template}")
            else:
                print("没有找到模板")
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
