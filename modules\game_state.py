#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏状态检测模块
负责检测当前游戏状态
"""

import time
import logging
from enum import Enum
from typing import Optional

class GameState(Enum):
    """游戏状态枚举"""
    UNKNOWN = "unknown"
    IN_TOWN = "in_town"
    IN_DUNGEON = "in_dungeon"
    MAP_SELECTION = "map_selection"
    FLIP_CARD = "flip_card"
    LOADING = "loading"
    DISCONNECTED = "disconnected"

class GameStateDetector:
    """游戏状态检测器"""
    
    def __init__(self, image_detector, config):
        self.image_detector = image_detector
        self.config = config
        self.logger = logging.getLogger("GameStateDetector")
        self.current_state = GameState.UNKNOWN
        self.last_detection_time = 0
        self.detection_interval = config.get_detection_interval()
    
    def get_current_state(self, force_update: bool = False) -> GameState:
        """获取当前游戏状态"""
        current_time = time.time()
        
        # 如果距离上次检测时间太短且不强制更新，返回缓存的状态
        if not force_update and (current_time - self.last_detection_time) < self.detection_interval:
            return self.current_state
        
        # 检测当前状态
        new_state = self._detect_state()
        
        if new_state != self.current_state:
            self.logger.info(f"游戏状态变化: {self.current_state.value} -> {new_state.value}")
            self.current_state = new_state
        
        self.last_detection_time = current_time
        return self.current_state
    
    def _detect_state(self) -> GameState:
        """检测游戏状态"""
        try:
            # 检测翻牌界面（优先级最高）
            if self.image_detector.is_flip_card_open():
                return GameState.FLIP_CARD
            
            # 检测地图选择界面
            if self.image_detector.is_map_selection_open():
                return GameState.MAP_SELECTION
            
            # 检测是否在城镇
            if self.image_detector.is_in_town():
                return GameState.IN_TOWN
            
            # 检测是否在地下城
            if self.image_detector.is_in_dungeon():
                return GameState.IN_DUNGEON
            
            # 如果都检测不到，可能是加载中或断线
            return GameState.UNKNOWN
            
        except Exception as e:
            self.logger.error(f"状态检测失败: {e}")
            return GameState.UNKNOWN
    
    def wait_for_state(self, target_state: GameState, timeout: float = 30.0) -> bool:
        """等待特定状态"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            current_state = self.get_current_state(force_update=True)
            if current_state == target_state:
                return True
            time.sleep(0.5)
        
        self.logger.warning(f"等待状态超时: {target_state.value}")
        return False
    
    def wait_for_state_change(self, current_state: GameState, timeout: float = 30.0) -> Optional[GameState]:
        """等待状态改变"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            new_state = self.get_current_state(force_update=True)
            if new_state != current_state:
                return new_state
            time.sleep(0.5)
        
        self.logger.warning(f"等待状态改变超时: {current_state.value}")
        return None
    
    def is_in_town(self) -> bool:
        """是否在城镇"""
        return self.get_current_state() == GameState.IN_TOWN
    
    def is_in_dungeon(self) -> bool:
        """是否在地下城"""
        return self.get_current_state() == GameState.IN_DUNGEON
    
    def is_map_selection_open(self) -> bool:
        """地图选择界面是否打开"""
        return self.get_current_state() == GameState.MAP_SELECTION
    
    def is_flip_card_open(self) -> bool:
        """翻牌界面是否打开"""
        return self.get_current_state() == GameState.FLIP_CARD
