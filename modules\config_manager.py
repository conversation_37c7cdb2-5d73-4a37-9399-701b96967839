#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责读取和管理配置文件
"""

import os
import configparser
from typing import Dict, Any

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file="config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置文件"""
        self.config['GENERAL'] = {
            'work_hours': '8',
            'skill_key': 'e'
        }
        
        self.config['FEATURES'] = {
            'use_stall_decompose': 'true',
            'enable_abyss': 'false'
        }
        
        self.config['HOTKEYS'] = {
            'auto_pickup': 'alt+q',
            'quick_repair': 'alt+w',
            'direct_decompose': 'alt+d',
            'stall_decompose': '0',
            'next_room': 'alt+right'
        }
        
        self.config['DETECTION'] = {
            'bag_full_threshold': '80',
            'image_threshold': '0.8',
            'detection_interval': '0.5'
        }
        
        self.config['PATHS'] = {
            'images_path': 'images/',
            'log_path': 'logs/'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def get_work_hours(self) -> int:
        """获取工作时长（小时）"""
        return self.config.getint('GENERAL', 'work_hours', fallback=8)
    
    def get_skill_key(self) -> str:
        """获取技能释放键"""
        return self.config.get('GENERAL', 'skill_key', fallback='e')
    
    def use_stall_decompose(self) -> bool:
        """是否使用摆摊分解"""
        return self.config.getboolean('FEATURES', 'use_stall_decompose', fallback=True)
    
    def enable_abyss(self) -> bool:
        """是否开启深渊"""
        return self.config.getboolean('FEATURES', 'enable_abyss', fallback=False)
    
    def get_hotkey(self, key_name: str) -> str:
        """获取热键配置"""
        return self.config.get('HOTKEYS', key_name, fallback='')
    
    def get_bag_full_threshold(self) -> int:
        """获取背包满度阈值"""
        return self.config.getint('DETECTION', 'bag_full_threshold', fallback=80)
    
    def get_image_threshold(self) -> float:
        """获取图像识别阈值"""
        return self.config.getfloat('DETECTION', 'image_threshold', fallback=0.8)
    
    def get_detection_interval(self) -> float:
        """获取检测间隔"""
        return self.config.getfloat('DETECTION', 'detection_interval', fallback=0.5)
    
    def get_images_path(self) -> str:
        """获取图像资源路径"""
        return self.config.get('PATHS', 'images_path', fallback='images/')
    
    def get_log_path(self) -> str:
        """获取日志路径"""
        return self.config.get('PATHS', 'log_path', fallback='logs/')
    
    def update_config(self, section: str, key: str, value: str):
        """更新配置项"""
        if section not in self.config:
            self.config.add_section(section)
        self.config.set(section, key, value)
        self.save_config()
    
    def get_all_hotkeys(self) -> Dict[str, str]:
        """获取所有热键配置"""
        return dict(self.config['HOTKEYS']) if 'HOTKEYS' in self.config else {}
