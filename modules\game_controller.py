#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏控制器模块
实现主要的游戏逻辑控制
"""

import time
import logging
from typing import Optional

from .game_state import GameState, GameStateDetector
from .inventory_manager import InventoryManager

class GameController:
    """游戏控制器"""
    
    def __init__(self, input_simulator, image_detector, config):
        self.input_sim = input_simulator
        self.image_detector = image_detector
        self.config = config
        self.logger = logging.getLogger("GameController")
        
        # 初始化子模块
        self.state_detector = GameStateDetector(image_detector, config)
        self.inventory_manager = InventoryManager(input_simulator, image_detector, config)
        
        # 技能释放键
        self.skill_key = config.get_skill_key()
        
        # 运行状态
        self.running = False
    
    def execute_main_logic(self):
        """执行主要游戏逻辑"""
        try:
            self.logger.info("开始执行主要游戏逻辑")
            
            # 检查当前游戏状态
            current_state = self.state_detector.get_current_state(force_update=True)
            self.logger.info(f"当前游戏状态: {current_state.value}")
            
            if current_state == GameState.IN_TOWN:
                self.handle_in_town()
            elif current_state == GameState.IN_DUNGEON:
                self.handle_in_dungeon()
            elif current_state == GameState.MAP_SELECTION:
                self.handle_map_selection()
            elif current_state == GameState.FLIP_CARD:
                self.handle_flip_card()
            else:
                self.logger.warning(f"未知游戏状态: {current_state.value}")
                time.sleep(2.0)
                
        except Exception as e:
            self.logger.error(f"执行主要游戏逻辑失败: {e}")
            time.sleep(5.0)
    
    def handle_in_town(self):
        """处理在城镇的逻辑"""
        self.logger.info("当前在城镇，准备进入格兰迪")
        
        # 检查背包是否满了
        if self.inventory_manager.is_bag_nearly_full():
            self.logger.info("背包接近满了，先处理背包")
            if not self.inventory_manager.handle_full_bag():
                self.logger.error("处理背包失败")
                return
        
        # 向右走进入地图选择页面
        self.input_sim.press_key('right')
        time.sleep(1.0)
        
        # 等待地图选择界面打开
        if self.state_detector.wait_for_state(GameState.MAP_SELECTION, timeout=10.0):
            self.logger.info("成功进入地图选择界面")
        else:
            self.logger.error("进入地图选择界面失败")
    
    def handle_map_selection(self):
        """处理地图选择界面的逻辑"""
        self.logger.info("在地图选择界面，选择格兰迪")
        
        # 查找格兰迪选项
        grandi_pos = self.image_detector.find_grandi_option()
        if grandi_pos:
            # 点击格兰迪
            self.input_sim.click_mouse(grandi_pos[0], grandi_pos[1])
            time.sleep(0.5)
            
            # 选择地狱难度
            hell_pos = self.image_detector.find_hell_difficulty()
            if hell_pos:
                self.input_sim.click_mouse(hell_pos[0], hell_pos[1])
                time.sleep(0.5)
            
            # 如果配置要求开启深渊
            if self.config.enable_abyss():
                abyss_pos = self.image_detector.find_abyss_button()
                if abyss_pos:
                    self.input_sim.press_key('f11')  # 开启深渊
                    time.sleep(0.5)
            
            # 按空格确定进入
            self.input_sim.press_key('space')
            time.sleep(2.0)
            
            # 等待进入地下城
            if self.state_detector.wait_for_state(GameState.IN_DUNGEON, timeout=15.0):
                self.logger.info("成功进入格兰迪")
            else:
                self.logger.error("进入格兰迪失败")
        else:
            self.logger.error("未找到格兰迪选项")
    
    def handle_in_dungeon(self):
        """处理在地下城的逻辑"""
        self.logger.info("在地下城中，开始打怪")
        
        # 移动到房间右侧并攻击
        self.move_and_attack()
        
        # 检查是否可以进入下一个房间
        if self.can_move_to_next_room():
            self.move_to_next_room()
        else:
            # 继续攻击当前房间
            self.attack_current_room()
    
    def handle_flip_card(self):
        """处理翻牌界面的逻辑"""
        self.logger.info("在翻牌界面，准备返回城镇")
        
        # 按ESC退出翻牌界面
        self.input_sim.press_key('esc')
        time.sleep(1.0)
        
        # 查找返回城镇按钮
        return_town_pos = self.image_detector.find_return_town_button()
        if return_town_pos:
            self.input_sim.click_mouse(return_town_pos[0], return_town_pos[1])
            time.sleep(2.0)
            
            # 等待返回城镇
            if self.state_detector.wait_for_state(GameState.IN_TOWN, timeout=15.0):
                self.logger.info("成功返回城镇")
                # 按数字键4（可能是某个功能键）
                self.press_number_4()
            else:
                self.logger.error("返回城镇失败")
        else:
            # 如果没找到返回城镇按钮，再按一次ESC
            self.input_sim.press_key('esc')
            time.sleep(1.0)
    
    def move_and_attack(self):
        """移动并攻击"""
        # 向右移动到房间门口
        self.input_sim.press_key('right', duration=1.0)
        time.sleep(0.5)
        
        # 转身攻击
        self.attack_current_room()
    
    def attack_current_room(self):
        """攻击当前房间的怪物"""
        # 释放技能攻击
        self.input_sim.press_key(self.skill_key)
        time.sleep(0.1)
        
        # 可以连续释放几次技能确保清空房间
        for _ in range(3):
            self.input_sim.press_key(self.skill_key)
            time.sleep(0.1)
    
    def can_move_to_next_room(self) -> bool:
        """检查是否可以移动到下一个房间"""
        # 这里需要通过小地图或其他方式判断房间是否清空
        # 暂时使用简单的延时判断
        time.sleep(1.0)
        return True
    
    def move_to_next_room(self):
        """移动到下一个房间"""
        self.logger.info("移动到下一个房间")
        
        # 使用Alt+Right顺图
        self.input_sim.press_key_combination(['alt', 'right'])
        time.sleep(1.0)
    
    def return_to_town_via_teleport(self):
        """通过传送返回城镇"""
        self.logger.info("通过传送返回城镇")
        
        # 查找传送按钮
        teleport_pos = self.image_detector.find_teleport_button()
        if teleport_pos:
            self.input_sim.click_mouse(teleport_pos[0], teleport_pos[1])
            time.sleep(1.0)
            
            # 查找搬砖圣地
            brick_land_pos = self.image_detector.find_brick_land()
            if brick_land_pos:
                self.input_sim.click_mouse(brick_land_pos[0], brick_land_pos[1])
                time.sleep(2.0)
                
                # 按ESC取消传送选择页面
                self.input_sim.press_key('esc')
                time.sleep(1.0)
                
                return True
        
        return False
    
    # 快捷键相关方法（供HotkeyManager调用）
    def quick_repair_equipment(self):
        """快速修理装备"""
        self.inventory_manager.quick_repair_equipment()
    
    def direct_decompose(self):
        """直接分解"""
        self.inventory_manager.direct_decompose()
    
    def stall_decompose(self):
        """摆摊分解"""
        self.inventory_manager.stall_decompose()
    
    def open_bag(self):
        """打开背包"""
        self.inventory_manager.open_bag()
    
    def press_number_4(self):
        """按数字键4"""
        self.input_sim.press_key('4')
        time.sleep(0.5)
