#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF高级输入测试
尝试多种不同的输入方法来控制角色
"""

import ctypes
import time
from ctypes import wintypes, Structure, Union, POINTER, byref

# Windows API 常量
WM_KEYDOWN = 0x0100
WM_KEYUP = 0x0101
WM_CHAR = 0x0102
WM_SYSKEYDOWN = 0x0104
WM_SYSKEYUP = 0x0105

# SendInput 相关结构
class POINT(Structure):
    _fields_ = [("x", ctypes.c_long), ("y", ctypes.c_long)]

class MOUSEINPUT(Structure):
    _fields_ = [("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", wintypes.DWORD),
                ("dwFlags", wintypes.DWORD),
                ("time", wintypes.DWORD),
                ("dwExtraInfo", POINTER(wintypes.ULONG))]

class KEYBDINPUT(Structure):
    _fields_ = [("wVk", wintypes.WORD),
                ("wScan", wintypes.WORD),
                ("dwFlags", wintypes.DWORD),
                ("time", wintypes.DWORD),
                ("dwExtraInfo", POINTER(wintypes.ULONG))]

class HARDWAREINPUT(Structure):
    _fields_ = [("uMsg", wintypes.DWORD),
                ("wParamL", wintypes.WORD),
                ("wParamH", wintypes.WORD)]

class INPUT_UNION(Union):
    _fields_ = [("ki", KEYBDINPUT),
                ("mi", MOUSEINPUT),
                ("hi", HARDWAREINPUT)]

class INPUT(Structure):
    _fields_ = [("type", wintypes.DWORD),
                ("ii", INPUT_UNION)]

# 虚拟键码
VK_CODE = {
    'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46,
    'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A, 'k': 0x4B, 'l': 0x4C,
    'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
    's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58,
    'y': 0x59, 'z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
    '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
    'space': 0x20, 'enter': 0x0D, 'esc': 0x1B, 'tab': 0x09,
    'shift': 0x10, 'ctrl': 0x11, 'alt': 0x12,
    'left': 0x25, 'up': 0x26, 'right': 0x27, 'down': 0x28,
}

class AdvancedInputTester:
    """高级输入测试器"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.game_hwnd = None
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or 
                        "Fighter" in window_title or
                        "地下城与勇士" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title} (句柄: {hwnd})")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def activate_window(self):
        """激活窗口"""
        if self.game_hwnd:
            self.user32.SetForegroundWindow(self.game_hwnd)
            self.user32.ShowWindow(self.game_hwnd, 9)
            time.sleep(0.5)
            return True
        return False
    
    def method1_postmessage(self, key):
        """方法1: PostMessage (已知有效但只对聊天框)"""
        vk_code = VK_CODE.get(key.lower())
        if not vk_code:
            return False
        
        print(f"方法1 - PostMessage: {key}")
        
        scan_code = self.user32.MapVirtualKeyW(vk_code, 0)
        lParam_down = (scan_code << 16) | 1
        lParam_up = (scan_code << 16) | 0xC0000001
        
        self.user32.PostMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)
        time.sleep(0.05)
        self.user32.PostMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)
        return True
    
    def method2_sendmessage(self, key):
        """方法2: SendMessage (同步发送)"""
        vk_code = VK_CODE.get(key.lower())
        if not vk_code:
            return False
        
        print(f"方法2 - SendMessage: {key}")
        
        scan_code = self.user32.MapVirtualKeyW(vk_code, 0)
        lParam_down = (scan_code << 16) | 1
        lParam_up = (scan_code << 16) | 0xC0000001
        
        self.user32.SendMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)
        time.sleep(0.05)
        self.user32.SendMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)
        return True
    
    def method3_sendinput(self, key):
        """方法3: SendInput (模拟硬件输入)"""
        vk_code = VK_CODE.get(key.lower())
        if not vk_code:
            return False
        
        print(f"方法3 - SendInput: {key}")
        
        # 激活窗口
        self.activate_window()
        
        # 创建输入结构
        inputs = (INPUT * 2)()
        
        # 按下
        inputs[0].type = 1  # INPUT_KEYBOARD
        inputs[0].ii.ki.wVk = vk_code
        inputs[0].ii.ki.wScan = 0
        inputs[0].ii.ki.dwFlags = 0
        inputs[0].ii.ki.time = 0
        inputs[0].ii.ki.dwExtraInfo = None
        
        # 释放
        inputs[1].type = 1  # INPUT_KEYBOARD
        inputs[1].ii.ki.wVk = vk_code
        inputs[1].ii.ki.wScan = 0
        inputs[1].ii.ki.dwFlags = 2  # KEYEVENTF_KEYUP
        inputs[1].ii.ki.time = 0
        inputs[1].ii.ki.dwExtraInfo = None
        
        # 发送输入
        result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
        print(f"SendInput结果: {result}")
        return result == 2
    
    def method4_keybd_event(self, key):
        """方法4: keybd_event (全局按键事件)"""
        vk_code = VK_CODE.get(key.lower())
        if not vk_code:
            return False
        
        print(f"方法4 - keybd_event: {key}")
        
        # 激活窗口
        self.activate_window()
        
        # 发送按键
        self.user32.keybd_event(vk_code, 0, 0, 0)  # 按下
        time.sleep(0.05)
        self.user32.keybd_event(vk_code, 0, 2, 0)  # 释放 (KEYEVENTF_KEYUP = 2)
        return True
    
    def method5_scan_code(self, key):
        """方法5: 使用扫描码的PostMessage"""
        vk_code = VK_CODE.get(key.lower())
        if not vk_code:
            return False
        
        print(f"方法5 - 扫描码PostMessage: {key}")
        
        scan_code = self.user32.MapVirtualKeyW(vk_code, 0)
        
        # 使用扫描码构造lParam
        lParam_down = (scan_code << 16) | 1
        lParam_up = (scan_code << 16) | 0xC0000001
        
        # 尝试发送到不同的消息
        self.user32.PostMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)
        self.user32.PostMessageW(self.game_hwnd, WM_CHAR, ord(key.lower()) if len(key) == 1 else vk_code, lParam_down)
        time.sleep(0.05)
        self.user32.PostMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)
        return True
    
    def method6_direct_scan(self, key):
        """方法6: 直接使用扫描码"""
        vk_code = VK_CODE.get(key.lower())
        if not vk_code:
            return False
        
        print(f"方法6 - 直接扫描码: {key}")
        
        # 激活窗口
        self.activate_window()
        
        scan_code = self.user32.MapVirtualKeyW(vk_code, 0)
        
        # 使用扫描码发送
        self.user32.keybd_event(0, scan_code, 4, 0)  # KEYEVENTF_SCANCODE = 4
        time.sleep(0.05)
        self.user32.keybd_event(0, scan_code, 6, 0)  # KEYEVENTF_SCANCODE | KEYEVENTF_KEYUP
        return True

def main():
    """主函数"""
    print("DNF高级输入测试工具")
    print("=" * 40)
    print("尝试多种不同的输入方法来控制角色")
    print()
    
    tester = AdvancedInputTester()
    
    if not tester.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    print("请确保:")
    print("1. DNF角色在可以移动的状态（不在对话中）")
    print("2. 没有打开聊天框")
    print("3. 角色可以自由移动")
    print()
    
    while True:
        print("\n选择测试方法:")
        print("1. PostMessage (已知对聊天框有效)")
        print("2. SendMessage (同步发送)")
        print("3. SendInput (硬件模拟)")
        print("4. keybd_event (全局按键)")
        print("5. 扫描码PostMessage")
        print("6. 直接扫描码")
        print("7. 测试所有方法")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-7): ").strip()
        
        if choice == '0':
            break
        elif choice in ['1', '2', '3', '4', '5', '6']:
            key = input("请输入要测试的按键 (如: right, e, space): ").strip()
            if not key:
                continue
            
            print(f"\n测试按键: {key}")
            print("请观察游戏中的反应...")
            
            if choice == '1':
                tester.method1_postmessage(key)
            elif choice == '2':
                tester.method2_sendmessage(key)
            elif choice == '3':
                tester.method3_sendinput(key)
            elif choice == '4':
                tester.method4_keybd_event(key)
            elif choice == '5':
                tester.method5_scan_code(key)
            elif choice == '6':
                tester.method6_direct_scan(key)
                
        elif choice == '7':
            key = input("请输入要测试的按键 (如: right): ").strip()
            if not key:
                continue
            
            print(f"\n测试所有方法，按键: {key}")
            print("请观察哪种方法能让角色移动...")
            
            methods = [
                ("PostMessage", tester.method1_postmessage),
                ("SendMessage", tester.method2_sendmessage),
                ("SendInput", tester.method3_sendinput),
                ("keybd_event", tester.method4_keybd_event),
                ("扫描码PostMessage", tester.method5_scan_code),
                ("直接扫描码", tester.method6_direct_scan)
            ]
            
            for name, method in methods:
                print(f"\n正在测试: {name}")
                method(key)
                time.sleep(2)  # 等待观察效果
                
        else:
            print("无效选择")
    
    print("测试结束")

if __name__ == "__main__":
    main()
