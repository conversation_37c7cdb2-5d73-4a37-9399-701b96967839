#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF搬砖脚本 - 单步测试版
可以逐步测试每个功能
"""

import ctypes
import time
import configparser
import os

# 虚拟键码
VK_CODE = {
    'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46,
    'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A, 'k': 0x4B, 'l': 0x4C,
    'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
    's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58,
    'y': 0x59, 'z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
    '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
    'space': 0x20, 'enter': 0x0D, 'esc': 0x1B, 'tab': 0x09,
    'shift': 0x10, 'ctrl': 0x11, 'alt': 0x12,
    'left': 0x25, 'up': 0x26, 'right': 0x27, 'down': 0x28,
}

class DNFBotStep:
    """DNF搬砖机器人 - 单步版"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.skill_key = 'e'  # 默认技能键
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            print(f"从配置文件加载技能键: {self.skill_key}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or 
                        "Fighter" in window_title or
                        "地下城与勇士" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title} (句柄: {hwnd})")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def activate_window(self):
        """激活窗口"""
        if self.game_hwnd:
            self.user32.SetForegroundWindow(self.game_hwnd)
            self.user32.ShowWindow(self.game_hwnd, 9)
            time.sleep(0.2)
            return True
        return False
    
    def send_key(self, key, duration=0.05):
        """发送按键到游戏窗口"""
        if not self.game_hwnd:
            print("错误: 游戏窗口句柄无效")
            return False
        
        vk_code = VK_CODE.get(key.lower())
        if vk_code is None:
            print(f"错误: 未知按键 {key}")
            return False
        
        try:
            WM_KEYDOWN = 0x0100
            WM_KEYUP = 0x0101
            
            scan_code = self.user32.MapVirtualKeyW(vk_code, 0)
            lParam_down = (scan_code << 16) | 1
            lParam_up = (scan_code << 16) | 0xC0000001
            
            print(f"发送按键: {key}")
            
            self.user32.PostMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)
            time.sleep(duration)
            self.user32.PostMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)
            
            return True
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False
    
    def send_combination(self, keys, duration=0.05):
        """发送组合键"""
        if not self.game_hwnd:
            print("错误: 游戏窗口句柄无效")
            return False
        
        vk_codes = []
        for key in keys:
            vk_code = VK_CODE.get(key.lower())
            if vk_code is None:
                print(f"错误: 未知按键 {key}")
                return False
            vk_codes.append(vk_code)
        
        try:
            WM_KEYDOWN = 0x0100
            WM_KEYUP = 0x0101
            
            print(f"发送组合键: {'+'.join(keys)}")
            
            # 按下所有键
            for vk_code in vk_codes:
                scan_code = self.user32.MapVirtualKeyW(vk_code, 0)
                lParam_down = (scan_code << 16) | 1
                self.user32.PostMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)
            
            time.sleep(duration)
            
            # 释放所有键（逆序）
            for vk_code in reversed(vk_codes):
                scan_code = self.user32.MapVirtualKeyW(vk_code, 0)
                lParam_up = (scan_code << 16) | 0xC0000001
                self.user32.PostMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)
            
            return True
        except Exception as e:
            print(f"发送组合键失败: {e}")
            return False

def main():
    """主函数"""
    print("DNF搬砖脚本 - 单步测试版")
    print("=" * 40)
    
    bot = DNFBotStep()
    
    if not bot.find_dnf_window():
        print("错误: 未找到DNF窗口")
        print("请确保DNF游戏已启动且窗口可见")
        return
    
    print(f"当前技能键设置: {bot.skill_key}")
    print()
    
    while True:
        print("\n请选择要测试的功能:")
        print("1. 激活游戏窗口")
        print("2. 测试移动 (右键)")
        print("3. 测试技能释放")
        print("4. 测试ESC键")
        print("5. 测试空格键")
        print("6. 测试组合键 Alt+Right")
        print("7. 测试数字键4")
        print("8. 完整搬砖流程测试")
        print("9. 自定义按键测试")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-9): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            if bot.activate_window():
                print("✓ 窗口激活成功")
            else:
                print("✗ 窗口激活失败")
        elif choice == '2':
            print("测试向右移动...")
            bot.send_key('right', duration=1.0)
        elif choice == '3':
            print(f"测试技能释放 ({bot.skill_key})...")
            for i in range(3):
                bot.send_key(bot.skill_key)
                time.sleep(0.2)
        elif choice == '4':
            print("测试ESC键...")
            bot.send_key('esc')
        elif choice == '5':
            print("测试空格键...")
            bot.send_key('space')
        elif choice == '6':
            print("测试Alt+Right组合键...")
            bot.send_combination(['alt', 'right'])
        elif choice == '7':
            print("测试数字键4...")
            bot.send_key('4')
        elif choice == '8':
            print("完整搬砖流程测试")
            print("请确保角色在城镇，准备好后按回车...")
            input()
            
            print("1. 进入地图选择...")
            bot.send_key('right')
            time.sleep(2)
            
            print("2. 确认进入地图...")
            bot.send_key('space')
            time.sleep(3)
            
            print("3. 开始打怪循环...")
            for room in range(3):  # 只测试3个房间
                print(f"   房间 {room+1}:")
                print("   - 向右移动")
                bot.send_key('right', duration=1.0)
                time.sleep(1)
                
                print(f"   - 释放技能 {bot.skill_key}")
                for _ in range(3):
                    bot.send_key(bot.skill_key)
                    time.sleep(0.2)
                
                if room < 2:
                    print("   - 顺图")
                    bot.send_combination(['alt', 'right'])
                    time.sleep(2)
            
            print("4. 退出地图...")
            for _ in range(3):
                bot.send_key('esc')
                time.sleep(1)
            
            print("5. 按数字键4...")
            bot.send_key('4')
            
            print("完整流程测试完成!")
            
        elif choice == '9':
            key = input("请输入要测试的按键: ").strip()
            if key:
                bot.send_key(key)
        else:
            print("无效选择")
    
    print("测试结束")

if __name__ == "__main__":
    main()
