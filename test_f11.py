#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试F11按键是否有效
"""

import ctypes
import time
from ctypes import wintypes, byref

class F11Tester:
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.game_hwnd = None
        self.enable_debug_privilege()
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                print("✓ 调试权限已启用")
        except Exception as e:
            print(f"调试权限启用失败: {e}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def test_f11_method1(self):
        """方法1: 标准F11 (扫描码0x57)"""
        print("方法1: 标准F11 (扫描码0x57)")
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.2)
        
        try:
            scan_code = 0x57  # F11扫描码
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(0.1)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            print("  ✓ F11已发送")
            return True
        except Exception as e:
            print(f"  ✗ F11发送失败: {e}")
            return False
    
    def test_f11_method2(self):
        """方法2: 虚拟键码F11 (VK_F11 = 0x7A)"""
        print("方法2: 虚拟键码F11 (VK_F11 = 0x7A)")
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.2)
        
        try:
            vk_code = 0x7A  # VK_F11
            self.user32.keybd_event(vk_code, 0, 0, 0)  # 按下
            time.sleep(0.1)
            self.user32.keybd_event(vk_code, 0, 0x0002, 0)  # 释放
            print("  ✓ F11已发送")
            return True
        except Exception as e:
            print(f"  ✗ F11发送失败: {e}")
            return False
    
    def test_f11_method3(self):
        """方法3: SendInput方式"""
        print("方法3: SendInput方式")
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.2)
        
        try:
            from ctypes import Structure, Union, POINTER
            
            class KEYBDINPUT(Structure):
                _fields_ = [("wVk", wintypes.WORD), ("wScan", wintypes.WORD),
                           ("dwFlags", wintypes.DWORD), ("time", wintypes.DWORD),
                           ("dwExtraInfo", POINTER(wintypes.ULONG))]
            
            class INPUT_UNION(Union):
                _fields_ = [("ki", KEYBDINPUT)]
            
            class INPUT(Structure):
                _fields_ = [("type", wintypes.DWORD), ("ii", INPUT_UNION)]
            
            inputs = (INPUT * 2)()
            
            # 按下F11
            inputs[0].type = 1  # INPUT_KEYBOARD
            inputs[0].ii.ki.wVk = 0x7A  # VK_F11
            inputs[0].ii.ki.wScan = 0x57  # F11扫描码
            inputs[0].ii.ki.dwFlags = 0
            inputs[0].ii.ki.time = 0
            inputs[0].ii.ki.dwExtraInfo = None
            
            # 释放F11
            inputs[1].type = 1
            inputs[1].ii.ki.wVk = 0x7A
            inputs[1].ii.ki.wScan = 0x57
            inputs[1].ii.ki.dwFlags = 0x0002  # KEYEVENTF_KEYUP
            inputs[1].ii.ki.time = 0
            inputs[1].ii.ki.dwExtraInfo = None
            
            result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
            print(f"  ✓ SendInput结果: {result}")
            return result == 2
            
        except Exception as e:
            print(f"  ✗ SendInput失败: {e}")
            return False
    
    def test_f11_multiple(self):
        """方法4: 多次按F11"""
        print("方法4: 多次按F11")
        
        for i in range(3):
            print(f"  第{i+1}次按F11...")
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.3)
            
            scan_code = 0x57
            self.user32.keybd_event(0, scan_code, 0x0008, 0)
            time.sleep(0.1)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
            time.sleep(0.5)
        
        print("  ✓ 多次F11完成")
        return True
    
    def interactive_test(self):
        """交互式测试"""
        while True:
            print("\n" + "=" * 40)
            print("F11按键测试工具")
            print("=" * 40)
            print("请在地图选择界面测试F11是否能开启深渊")
            print()
            print("1. 方法1: 标准扫描码")
            print("2. 方法2: 虚拟键码")
            print("3. 方法3: SendInput")
            print("4. 方法4: 多次按F11")
            print("5. 测试所有方法")
            print("0. 退出")
            
            choice = input("\n请选择: ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                self.test_f11_method1()
                input("请观察深渊是否开启，按回车继续...")
            elif choice == '2':
                self.test_f11_method2()
                input("请观察深渊是否开启，按回车继续...")
            elif choice == '3':
                self.test_f11_method3()
                input("请观察深渊是否开启，按回车继续...")
            elif choice == '4':
                self.test_f11_multiple()
                input("请观察深渊是否开启，按回车继续...")
            elif choice == '5':
                print("测试所有方法...")
                methods = [
                    ("方法1", self.test_f11_method1),
                    ("方法2", self.test_f11_method2),
                    ("方法3", self.test_f11_method3),
                    ("方法4", self.test_f11_multiple),
                ]
                
                for name, method in methods:
                    print(f"\n{name}")
                    method()
                    response = input("深渊是否开启? (y/n): ").strip().lower()
                    if response == 'y':
                        print(f"✓ {name} 有效!")
                        break
                    else:
                        print(f"✗ {name} 无效")
                        time.sleep(1)
            else:
                print("无效选择")

def main():
    """主函数"""
    print("F11按键测试工具")
    print("=" * 30)
    print("专门测试F11是否能开启深渊模式")
    
    tester = F11Tester()
    
    if not tester.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    print("\n请确保:")
    print("1. 角色在地图选择界面")
    print("2. 可以看到深渊模式选项")
    print("3. 观察按F11后深渊是否开启")
    
    tester.interactive_test()

if __name__ == "__main__":
    main()
