#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像识别模块
负责游戏界面的图像识别和检测
"""

import cv2
import numpy as np
import win32gui
import win32ui
import win32con
from PIL import Image
import os
import logging
from typing import Tuple, Optional, List

class ImageDetector:
    """图像检测器"""
    
    def __init__(self, images_path="images/"):
        self.logger = logging.getLogger("ImageDetector")
        self.images_path = images_path
        self.game_hwnd = None
        self.template_cache = {}
        
        # 创建图像目录
        os.makedirs(self.images_path, exist_ok=True)
        
        # 预加载模板图像
        self.load_templates()
    
    def load_templates(self):
        """加载模板图像"""
        template_files = {
            'town_map': 'town_map.png',           # 城镇小地图
            'dungeon_map': 'dungeon_map.png',     # 地下城小地图
            'map_selection': 'map_selection.png', # 地图选择界面
            'grandi': 'grandi.png',               # 格兰迪选项
            'hell_difficulty': 'hell_difficulty.png', # 地狱难度
            'abyss_button': 'abyss_button.png',   # 深渊按钮
            'flip_card': 'flip_card.png',         # 翻牌界面
            'return_town': 'return_town.png',     # 返回城镇按钮
            'teleport_button': 'teleport_button.png', # 传送按钮
            'brick_land': 'brick_land.png',       # 搬砖圣地
            'stall_machine': 'stall_machine.png', # 摆摊分解机
            'decompose_panel': 'decompose_panel.png', # 分解面板
            'bag_full': 'bag_full.png',           # 背包满
            'repair_npc': 'repair_npc.png'        # 修理NPC
        }
        
        for name, filename in template_files.items():
            filepath = os.path.join(self.images_path, filename)
            if os.path.exists(filepath):
                try:
                    template = cv2.imread(filepath, cv2.IMREAD_COLOR)
                    if template is not None:
                        self.template_cache[name] = template
                        self.logger.info(f"加载模板图像: {name}")
                    else:
                        self.logger.warning(f"无法加载模板图像: {filepath}")
                except Exception as e:
                    self.logger.error(f"加载模板图像失败 {filepath}: {e}")
            else:
                self.logger.warning(f"模板图像不存在: {filepath}")
    
    def find_game_window(self, window_title="Dungeon") -> bool:
        """查找游戏窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if (window_title in window_text or
                    "Dungeon" in window_text or
                    "Fighter" in window_text or
                    "地下城与勇士" in window_text):
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            self.game_hwnd = windows[0]
            self.logger.info(f"找到游戏窗口: {win32gui.GetWindowText(self.game_hwnd)}")
            return True
        else:
            self.logger.warning("未找到游戏窗口")
            return False
    
    def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> Optional[np.ndarray]:
        """截取屏幕"""
        if not self.game_hwnd:
            if not self.find_game_window():
                return None
        
        try:
            # 获取窗口位置和大小
            left, top, right, bottom = win32gui.GetWindowRect(self.game_hwnd)
            width = right - left
            height = bottom - top
            
            # 创建设备上下文
            hwndDC = win32gui.GetWindowDC(self.game_hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # 创建位图
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            # 复制屏幕内容
            saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            
            # 转换为numpy数组
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            
            img = np.frombuffer(bmpstr, dtype='uint8')
            img.shape = (height, width, 4)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            # 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(self.game_hwnd, hwndDC)
            
            # 如果指定了区域，裁剪图像
            if region:
                x, y, w, h = region
                img = img[y:y+h, x:x+w]
            
            return img
            
        except Exception as e:
            self.logger.error(f"截屏失败: {e}")
            return None
    
    def find_template(self, template_name: str, threshold: float = 0.8, 
                     region: Optional[Tuple[int, int, int, int]] = None) -> Optional[Tuple[int, int]]:
        """查找模板图像"""
        if template_name not in self.template_cache:
            self.logger.warning(f"模板图像不存在: {template_name}")
            return None
        
        # 截取屏幕
        screen = self.capture_screen(region)
        if screen is None:
            return None
        
        template = self.template_cache[template_name]
        
        try:
            # 模板匹配
            result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= threshold:
                # 返回匹配位置的中心点
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                # 如果指定了区域，需要加上偏移
                if region:
                    center_x += region[0]
                    center_y += region[1]
                
                self.logger.debug(f"找到模板 {template_name} 在位置 ({center_x}, {center_y}), 相似度: {max_val:.3f}")
                return (center_x, center_y)
            else:
                self.logger.debug(f"未找到模板 {template_name}, 最高相似度: {max_val:.3f}")
                return None
                
        except Exception as e:
            self.logger.error(f"模板匹配失败 {template_name}: {e}")
            return None
    
    def is_in_town(self) -> bool:
        """检测是否在城镇"""
        return self.find_template('town_map') is not None
    
    def is_in_dungeon(self) -> bool:
        """检测是否在地下城"""
        return self.find_template('dungeon_map') is not None
    
    def is_map_selection_open(self) -> bool:
        """检测地图选择界面是否打开"""
        return self.find_template('map_selection') is not None
    
    def is_flip_card_open(self) -> bool:
        """检测翻牌界面是否打开"""
        return self.find_template('flip_card') is not None
    
    def find_grandi_option(self) -> Optional[Tuple[int, int]]:
        """查找格兰迪选项"""
        return self.find_template('grandi')
    
    def find_hell_difficulty(self) -> Optional[Tuple[int, int]]:
        """查找地狱难度选项"""
        return self.find_template('hell_difficulty')
    
    def find_abyss_button(self) -> Optional[Tuple[int, int]]:
        """查找深渊按钮"""
        return self.find_template('abyss_button')
    
    def find_return_town_button(self) -> Optional[Tuple[int, int]]:
        """查找返回城镇按钮"""
        return self.find_template('return_town')
    
    def find_teleport_button(self) -> Optional[Tuple[int, int]]:
        """查找传送按钮"""
        return self.find_template('teleport_button')
    
    def find_brick_land(self) -> Optional[Tuple[int, int]]:
        """查找搬砖圣地"""
        return self.find_template('brick_land')
    
    def is_bag_full(self) -> bool:
        """检测背包是否满了"""
        # 这里可以通过检测背包界面的满格状态
        # 或者通过颜色检测等方式判断
        return self.find_template('bag_full') is not None
    
    def get_minimap_region(self) -> Tuple[int, int, int, int]:
        """获取小地图区域坐标"""
        # 通常小地图在右上角，这里返回一个估计的区域
        # 实际使用时需要根据游戏分辨率调整
        return (1100, 50, 200, 200)  # x, y, width, height
    
    def save_screenshot(self, filename: str, region: Optional[Tuple[int, int, int, int]] = None):
        """保存截图"""
        screen = self.capture_screen(region)
        if screen is not None:
            filepath = os.path.join(self.images_path, filename)
            cv2.imwrite(filepath, screen)
            self.logger.info(f"截图已保存: {filepath}")
