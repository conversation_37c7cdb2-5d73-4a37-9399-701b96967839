#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF图像模板收集工具
帮助收集自动化所需的图像模板
"""

import ctypes
import time
import os
import cv2
import numpy as np
from PIL import ImageGrab
from ctypes import wintypes, byref

class TemplateCollector:
    """图像模板收集器"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.window_rect = None
        
        # 创建模板目录
        os.makedirs("templates", exist_ok=True)
        
        # 需要收集的模板列表
        self.templates_needed = [
            # 游戏状态
            ("town", "城镇界面 - 请在城镇中截取小地图或特征区域"),
            ("map_selection", "地图选择界面 - 请在地图选择界面截取标题或特征"),
            ("in_dungeon", "地下城内 - 请在地下城内截取小地图或界面特征"),
            
            # 地图选择
            ("grandi_option", "格兰迪选项 - 请截取格兰迪的文字或图标"),
            ("hell_difficulty", "地狱难度 - 请截取地狱难度选项"),
            ("abyss_button", "深渊按钮 - 请截取深渊模式按钮（可选）"),
            
            # 战斗状态
            ("monster_exists", "有怪物 - 请在有怪物时截取怪物血条或红点"),
            ("room_clear", "房间清空 - 请在房间清空后截取（没有红点的状态）"),
            ("boss_room", "BOSS房间 - 请截取BOSS房间的标识"),
            
            # 结束流程
            ("flip_card", "翻牌界面 - 请截取翻牌界面的特征"),
            ("return_town", "返回城镇按钮 - 请截取返回城镇按钮"),
            ("dungeon_complete", "地下城完成 - 请截取完成界面"),
            
            # 背包管理
            ("bag_full", "背包满 - 请截取背包满的提示"),
            ("stall_machine", "摆摊分解机 - 请截取分解机界面"),
            ("repair_needed", "需要修理 - 请截取装备需要修理的提示"),
        ]
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        print(f"找到DNF窗口: {window_title}")
                        print(f"窗口位置: {self.window_rect}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def capture_game_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.2)
            
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"截屏失败: {e}")
            return None
    
    def save_template(self, name, description):
        """保存模板图像"""
        print(f"\n=== 收集模板: {name} ===")
        print(f"说明: {description}")
        print("请按照说明调整游戏到对应状态")
        
        while True:
            choice = input("准备好后输入 's' 截图，'skip' 跳过，'view' 查看当前画面: ").strip().lower()
            
            if choice == 's':
                screen = self.capture_game_screen()
                if screen is not None:
                    # 显示截图让用户选择区域
                    self.select_region_and_save(screen, name, description)
                    break
                else:
                    print("截图失败，请重试")
            elif choice == 'skip':
                print(f"跳过 {name}")
                break
            elif choice == 'view':
                screen = self.capture_game_screen()
                if screen is not None:
                    # 显示当前画面
                    cv2.imshow(f"当前画面 - {name}", screen)
                    cv2.waitKey(0)
                    cv2.destroyAllWindows()
            else:
                print("无效输入，请输入 's'、'skip' 或 'view'")
    
    def select_region_and_save(self, screen, name, description):
        """选择区域并保存"""
        print(f"\n请在弹出的窗口中:")
        print("1. 用鼠标拖拽选择要识别的区域")
        print("2. 按空格键确认选择")
        print("3. 按ESC键取消")
        
        # 创建窗口
        cv2.namedWindow(f"选择区域 - {name}", cv2.WINDOW_NORMAL)
        cv2.resizeWindow(f"选择区域 - {name}", 1200, 800)
        
        # 全局变量用于鼠标回调
        self.selecting = False
        self.start_point = None
        self.end_point = None
        self.current_screen = screen.copy()
        
        def mouse_callback(event, x, y, flags, param):
            if event == cv2.EVENT_LBUTTONDOWN:
                self.selecting = True
                self.start_point = (x, y)
                self.end_point = (x, y)
            elif event == cv2.EVENT_MOUSEMOVE and self.selecting:
                self.end_point = (x, y)
                # 绘制选择框
                temp_screen = screen.copy()
                cv2.rectangle(temp_screen, self.start_point, self.end_point, (0, 255, 0), 2)
                cv2.imshow(f"选择区域 - {name}", temp_screen)
            elif event == cv2.EVENT_LBUTTONUP:
                self.selecting = False
                self.end_point = (x, y)
        
        cv2.setMouseCallback(f"选择区域 - {name}", mouse_callback)
        cv2.imshow(f"选择区域 - {name}", screen)
        
        while True:
            key = cv2.waitKey(1) & 0xFF
            if key == ord(' '):  # 空格键确认
                if self.start_point and self.end_point:
                    # 提取选择的区域
                    x1, y1 = self.start_point
                    x2, y2 = self.end_point
                    
                    # 确保坐标正确
                    x1, x2 = min(x1, x2), max(x1, x2)
                    y1, y2 = min(y1, y2), max(y1, y2)
                    
                    if x2 - x1 > 10 and y2 - y1 > 10:  # 确保区域足够大
                        template = screen[y1:y2, x1:x2]
                        
                        # 保存模板
                        filename = f"templates/{name}.png"
                        cv2.imwrite(filename, template)
                        print(f"✓ 模板已保存: {filename}")
                        print(f"  区域: ({x1}, {y1}) - ({x2}, {y2})")
                        print(f"  大小: {x2-x1} x {y2-y1}")
                        
                        # 显示保存的模板
                        cv2.imshow(f"已保存 - {name}", template)
                        cv2.waitKey(2000)  # 显示2秒
                        cv2.destroyAllWindows()
                        break
                    else:
                        print("选择区域太小，请重新选择")
                else:
                    print("请先选择区域")
            elif key == 27:  # ESC键取消
                print("取消选择")
                cv2.destroyAllWindows()
                break
    
    def collect_all_templates(self):
        """收集所有模板"""
        print("DNF图像模板收集工具")
        print("=" * 50)
        print("我们需要收集以下图像模板来实现自动化:")
        
        for i, (name, description) in enumerate(self.templates_needed, 1):
            print(f"{i}. {name}: {description}")
        
        print("\n开始收集模板...")
        
        for name, description in self.templates_needed:
            # 检查是否已存在
            if os.path.exists(f"templates/{name}.png"):
                choice = input(f"\n模板 {name}.png 已存在，是否重新收集? (y/n): ").strip().lower()
                if choice != 'y':
                    continue
            
            self.save_template(name, description)
        
        print("\n" + "=" * 50)
        print("模板收集完成!")
        self.show_collected_templates()
    
    def show_collected_templates(self):
        """显示已收集的模板"""
        print("\n已收集的模板:")
        template_dir = "templates"
        
        if os.path.exists(template_dir):
            templates = [f for f in os.listdir(template_dir) if f.endswith('.png')]
            
            if templates:
                for template in sorted(templates):
                    print(f"✓ {template}")
                print(f"\n总共收集了 {len(templates)} 个模板")
            else:
                print("没有找到模板文件")
        else:
            print("模板目录不存在")
    
    def test_template_matching(self):
        """测试模板匹配"""
        print("\n=== 模板匹配测试 ===")
        
        template_dir = "templates"
        if not os.path.exists(template_dir):
            print("模板目录不存在")
            return
        
        templates = [f for f in os.listdir(template_dir) if f.endswith('.png')]
        if not templates:
            print("没有找到模板文件")
            return
        
        print("可用模板:")
        for i, template in enumerate(templates, 1):
            print(f"{i}. {template}")
        
        choice = input("请输入要测试的模板编号: ").strip()
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(templates):
                template_name = templates[idx]
                self.match_template(template_name)
            else:
                print("无效编号")
        except ValueError:
            print("请输入数字")
    
    def match_template(self, template_name):
        """匹配模板"""
        template_path = f"templates/{template_name}"
        template = cv2.imread(template_path)
        
        if template is None:
            print(f"无法加载模板: {template_path}")
            return
        
        print(f"测试模板: {template_name}")
        print("按空格键截图并匹配，按ESC退出")
        
        while True:
            key = input("按回车截图匹配，输入 'q' 退出: ").strip()
            if key.lower() == 'q':
                break
            
            # 截取当前画面
            screen = self.capture_game_screen()
            if screen is None:
                print("截图失败")
                continue
            
            # 模板匹配
            result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            print(f"匹配度: {max_val:.3f}")
            
            if max_val > 0.7:  # 匹配阈值
                print(f"✓ 找到匹配! 位置: {max_loc}, 匹配度: {max_val:.3f}")
                
                # 在截图上标记匹配位置
                h, w = template.shape[:2]
                top_left = max_loc
                bottom_right = (top_left[0] + w, top_left[1] + h)
                
                result_img = screen.copy()
                cv2.rectangle(result_img, top_left, bottom_right, (0, 255, 0), 2)
                
                # 显示结果
                cv2.imshow("匹配结果", result_img)
                cv2.waitKey(3000)
                cv2.destroyAllWindows()
            else:
                print(f"✗ 未找到匹配 (匹配度: {max_val:.3f} < 0.7)")

def main():
    """主函数"""
    collector = TemplateCollector()
    
    if not collector.find_dnf_window():
        print("错误: 未找到DNF窗口")
        print("请确保DNF游戏已启动")
        return
    
    while True:
        print("\n" + "=" * 50)
        print("DNF图像模板收集工具")
        print("=" * 50)
        print("1. 收集所有模板")
        print("2. 收集单个模板")
        print("3. 查看已收集的模板")
        print("4. 测试模板匹配")
        print("0. 退出")
        
        choice = input("\n请选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            collector.collect_all_templates()
        elif choice == '2':
            print("\n可收集的模板:")
            for i, (name, desc) in enumerate(collector.templates_needed, 1):
                print(f"{i}. {name}: {desc}")
            
            try:
                idx = int(input("请输入模板编号: ")) - 1
                if 0 <= idx < len(collector.templates_needed):
                    name, desc = collector.templates_needed[idx]
                    collector.save_template(name, desc)
                else:
                    print("无效编号")
            except ValueError:
                print("请输入数字")
        elif choice == '3':
            collector.show_collected_templates()
        elif choice == '4':
            collector.test_template_matching()
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
