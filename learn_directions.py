#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF顺图方向学习工具
帮助机器人学习正确的顺图方向
"""

import ctypes
import time
import json
import os
from ctypes import wintypes, byref

# 扫描码
HARDWARE_SCAN_CODES = {
    'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0,
    'alt': 0x38, 'e': 0x12,
}

class DirectionLearner:
    """方向学习器"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        
        # 学习到的方向序列
        self.learned_directions = []
        
        self.enable_debug_privilege()
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = ctypes.windll.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                print("✓ 调试权限已启用")
        except Exception as e:
            print(f"调试权限启用失败: {e}")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def send_key(self, key):
        """发送按键"""
        scan_code = HARDWARE_SCAN_CODES.get(key.lower())
        if not scan_code:
            return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            return True
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False
    
    def send_combination(self, keys):
        """发送组合键"""
        scan_codes = []
        for key in keys:
            scan_code = HARDWARE_SCAN_CODES.get(key.lower())
            if scan_code:
                scan_codes.append(scan_code)
            else:
                return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            # 按下所有键
            for scan_code in scan_codes:
                self.user32.keybd_event(0, scan_code, 0x0008, 0)
                time.sleep(0.01)
            
            time.sleep(0.05)
            
            # 释放所有键（逆序）
            for scan_code in reversed(scan_codes):
                self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
                time.sleep(0.01)
            
            return True
        except Exception as e:
            print(f"发送组合键失败: {e}")
            return False
    
    def learn_grandi_directions(self):
        """学习格兰迪的顺图方向"""
        print("\n=== 格兰迪顺图方向学习 ===")
        print("我们将一起学习格兰迪8个房间的正确顺图方向")
        print()
        print("操作说明:")
        print("1. 请先手动进入格兰迪地下城")
        print("2. 我会帮您清理每个房间")
        print("3. 清理完成后，您告诉我下个房间的方向")
        print("4. 我会记录并保存这些方向")
        print()
        
        input("准备好后按回车开始...")
        
        self.learned_directions = []
        
        for room in range(8):
            print(f"\n--- 房间 {room+1}/8 ---")
            
            if room == 0:
                print("第一个房间：需要靠近地下怪物位置")
                print("向前移动...")
                for i in range(6):  # 多移动几步
                    self.send_key('right')
                    time.sleep(0.3)

                print("释放技能让地下怪物出现...")
                for i in range(3):
                    self.send_key('e')
                    time.sleep(0.5)

                print("继续向前走到怪物位置...")
                for i in range(3):
                    self.send_key('right')
                    time.sleep(0.3)

                print("现在应该能看到地下怪物了")
            
            # 清理房间
            print("开始清理房间...")
            self.clear_room_interactive()
            
            # 学习方向（除了最后一个房间）
            if room < 7:
                direction = self.ask_direction(room + 1)
                if direction:
                    self.learned_directions.append(direction)
                    print(f"记录方向: 房间{room+1} → 房间{room+2} = Alt+{direction}")
                    
                    # 执行顺图
                    print(f"执行 Alt+{direction}...")
                    self.send_combination(['alt', direction])
                    time.sleep(2.5)
                else:
                    print("跳过此房间")
                    break
        
        # 保存学习结果
        self.save_directions()
        print(f"\n学习完成！共记录了 {len(self.learned_directions)} 个方向")
    
    def clear_room_interactive(self):
        """交互式清理房间"""
        print("清理房间中...")
        
        while True:
            # 攻击
            for _ in range(5):
                self.send_key('e')
                time.sleep(0.2)
            
            # 移动
            self.send_key('right')
            time.sleep(0.1)
            
            choice = input("房间是否清空? (y/继续清理): ").strip().lower()
            if choice == 'y':
                print("房间清理完成")
                break
    
    def ask_direction(self, next_room):
        """询问下个房间方向"""
        print(f"\n请观察小地图，房间{next_room}在当前房间的哪个方向？")
        print("1. 上方 (up)")
        print("2. 下方 (down)")
        print("3. 左方 (left)")
        print("4. 右方 (right)")
        print("5. 跳过")
        
        while True:
            choice = input("请选择方向 (1-5): ").strip()
            
            if choice == '1':
                return 'up'
            elif choice == '2':
                return 'down'
            elif choice == '3':
                return 'left'
            elif choice == '4':
                return 'right'
            elif choice == '5':
                return None
            else:
                print("无效选择，请重新输入")
    
    def save_directions(self):
        """保存学习到的方向"""
        data = {
            'grandi_directions': self.learned_directions,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        with open('grandi_directions.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print("方向数据已保存到 grandi_directions.json")
    
    def load_directions(self):
        """加载已学习的方向"""
        if os.path.exists('grandi_directions.json'):
            try:
                with open('grandi_directions.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.learned_directions = data.get('grandi_directions', [])
                    print(f"已加载 {len(self.learned_directions)} 个方向")
                    return True
            except Exception as e:
                print(f"加载方向数据失败: {e}")
        
        return False
    
    def show_learned_directions(self):
        """显示已学习的方向"""
        if not self.learned_directions:
            print("还没有学习任何方向")
            return
        
        print("\n已学习的格兰迪顺图方向:")
        for i, direction in enumerate(self.learned_directions):
            print(f"房间{i+1} → 房间{i+2}: Alt+{direction}")
    
    def test_learned_directions(self):
        """测试已学习的方向"""
        if not self.learned_directions:
            print("还没有学习任何方向")
            return
        
        print("\n测试已学习的方向...")
        print("请先进入格兰迪地下城第一个房间")
        input("准备好后按回车...")
        
        for i, direction in enumerate(self.learned_directions):
            print(f"\n房间{i+1} → 房间{i+2}: Alt+{direction}")
            choice = input("是否执行此方向? (y/n): ").strip().lower()
            
            if choice == 'y':
                self.send_combination(['alt', direction])
                time.sleep(2.5)
                print(f"已执行 Alt+{direction}")
            else:
                print("跳过")

def main():
    """主函数"""
    learner = DirectionLearner()
    
    if not learner.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    # 尝试加载已有的方向数据
    learner.load_directions()
    
    while True:
        print("\n" + "=" * 40)
        print("DNF顺图方向学习工具")
        print("=" * 40)
        print("1. 学习格兰迪顺图方向")
        print("2. 查看已学习的方向")
        print("3. 测试已学习的方向")
        print("4. 重新学习")
        print("0. 退出")
        
        choice = input("\n请选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            if learner.learned_directions:
                print("已有学习数据，是否重新学习?")
                confirm = input("输入 'yes' 重新学习: ").strip()
                if confirm != 'yes':
                    continue
            learner.learn_grandi_directions()
        elif choice == '2':
            learner.show_learned_directions()
        elif choice == '3':
            learner.test_learned_directions()
        elif choice == '4':
            learner.learned_directions = []
            learner.learn_grandi_directions()
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
