#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查文件是否存在
"""

import os

def check_files():
    """检查模板文件"""
    print("检查当前目录的文件...")
    print(f"当前工作目录: {os.getcwd()}")
    print()
    
    # 列出当前目录的所有文件
    files = os.listdir('.')
    print("当前目录的所有文件:")
    for file in sorted(files):
        if os.path.isfile(file):
            print(f"  📄 {file}")
        else:
            print(f"  📁 {file}/")
    
    print()
    
    # 检查模板文件
    required_files = ["normal_door.png", "boss_door.png"]
    
    print("检查模板文件:")
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  ✅ {file} (大小: {size} 字节)")
        else:
            print(f"  ❌ {file} - 文件不存在")
    
    print()
    
    # 检查PNG文件
    png_files = [f for f in files if f.lower().endswith('.png')]
    if png_files:
        print("找到的PNG文件:")
        for png in png_files:
            size = os.path.getsize(png)
            print(f"  🖼️ {png} (大小: {size} 字节)")
    else:
        print("当前目录没有PNG文件")

if __name__ == "__main__":
    check_files()
