#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF驱动级输入模拟
基于成功的DNF项目经验，使用驱动级别的输入方法
"""

import ctypes
import time
import os
from ctypes import wintypes, Structure, Union, POINTER, byref

# 驱动级输入常量
IOCTL_KEYBOARD_SET_INDICATORS = 0x000B0004
IOCTL_KEYBOARD_QUERY_ATTRIBUTES = 0x000B0000
IOCTL_KEYBOARD_QUERY_INDICATOR_TRANSLATION = 0x000B0010
IOCTL_KEYBOARD_QUERY_INDICATORS = 0x000B0020

# 键盘扫描码（硬件级别）
HARDWARE_SCAN_CODES = {
    'a': 0x1E, 'b': 0x30, 'c': 0x2E, 'd': 0x20, 'e': 0x12, 'f': 0x21,
    'g': 0x22, 'h': 0x23, 'i': 0x17, 'j': 0x24, 'k': 0x25, 'l': 0x26,
    'm': 0x32, 'n': 0x31, 'o': 0x18, 'p': 0x19, 'q': 0x10, 'r': 0x13,
    's': 0x1F, 't': 0x14, 'u': 0x16, 'v': 0x2F, 'w': 0x11, 'x': 0x2D,
    'y': 0x15, 'z': 0x2C,
    '0': 0x0B, '1': 0x02, '2': 0x03, '3': 0x04, '4': 0x05,
    '5': 0x06, '6': 0x07, '7': 0x08, '8': 0x09, '9': 0x0A,
    'space': 0x39, 'enter': 0x1C, 'esc': 0x01, 'tab': 0x0F,
    'shift': 0x2A, 'ctrl': 0x1D, 'alt': 0x38,
    'left': 0xCB, 'up': 0xC8, 'right': 0xCD, 'down': 0xD0,
    'f1': 0x3B, 'f2': 0x3C, 'f3': 0x3D, 'f4': 0x3E, 'f5': 0x3F,
}

class DNFDriverInput:
    """DNF驱动级输入模拟器"""
    
    def __init__(self):
        self.kernel32 = ctypes.windll.kernel32
        self.user32 = ctypes.windll.user32
        self.ntdll = ctypes.windll.ntdll
        self.game_hwnd = None
        self.game_pid = None
        
        # 尝试获取调试权限
        self.enable_debug_privilege()
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            # 获取当前进程令牌
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if not ctypes.windll.advapi32.OpenProcessToken(
                process, 0x0020 | 0x0008, byref(token)  # TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY
            ):
                return False
            
            # 查找调试权限
            luid = wintypes.LARGE_INTEGER()
            if not ctypes.windll.advapi32.LookupPrivilegeValueW(
                None, "SeDebugPrivilege", byref(luid)
            ):
                return False
            
            # 启用权限
            class TOKEN_PRIVILEGES(Structure):
                _fields_ = [
                    ("PrivilegeCount", wintypes.DWORD),
                    ("Luid", wintypes.LARGE_INTEGER),
                    ("Attributes", wintypes.DWORD),
                ]
            
            tp = TOKEN_PRIVILEGES()
            tp.PrivilegeCount = 1
            tp.Luid = luid
            tp.Attributes = 0x00000002  # SE_PRIVILEGE_ENABLED
            
            result = ctypes.windll.advapi32.AdjustTokenPrivileges(
                token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
            )
            
            ctypes.windll.kernel32.CloseHandle(token)
            print("✓ 调试权限已启用" if result else "✗ 调试权限启用失败")
            return bool(result)
            
        except Exception as e:
            print(f"启用调试权限失败: {e}")
            return False
    
    def find_dnf_window(self):
        """查找DNF窗口和进程"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or 
                        "Fighter" in window_title or
                        "地下城与勇士" in window_title):
                        
                        # 获取进程ID
                        pid = wintypes.DWORD()
                        self.user32.GetWindowThreadProcessId(hwnd, byref(pid))
                        
                        self.game_hwnd = hwnd
                        self.game_pid = pid.value
                        print(f"找到DNF窗口: {window_title}")
                        print(f"窗口句柄: {hwnd}, 进程ID: {self.game_pid}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def method1_direct_memory_write(self, scan_code):
        """方法1: 直接内存写入（最底层）"""
        if not self.game_pid:
            return False
        
        try:
            # 打开目标进程
            PROCESS_ALL_ACCESS = 0x1F0FFF
            process_handle = self.kernel32.OpenProcess(
                PROCESS_ALL_ACCESS, False, self.game_pid
            )
            
            if not process_handle:
                print("无法打开目标进程")
                return False
            
            print(f"方法1 - 直接内存写入: 扫描码 0x{scan_code:02X}")
            
            # 这里需要找到游戏的键盘输入缓冲区地址
            # 这是最底层的方法，但需要逆向工程找到正确的内存地址
            
            self.kernel32.CloseHandle(process_handle)
            return True
            
        except Exception as e:
            print(f"直接内存写入失败: {e}")
            return False
    
    def method2_hardware_simulation(self, scan_code):
        """方法2: 硬件级模拟"""
        try:
            print(f"方法2 - 硬件级模拟: 扫描码 0x{scan_code:02X}")
            
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 使用最底层的键盘端口模拟
            # 这模拟了键盘控制器的行为
            
            # 发送扫描码到键盘缓冲区
            # 按下
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # KEYEVENTF_SCANCODE
            time.sleep(0.05)
            # 释放
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # KEYEVENTF_SCANCODE | KEYEVENTF_KEYUP
            
            return True
            
        except Exception as e:
            print(f"硬件级模拟失败: {e}")
            return False
    
    def method3_driver_level_input(self, scan_code):
        """方法3: 驱动级输入（模拟成功项目的方法）"""
        try:
            print(f"方法3 - 驱动级输入: 扫描码 0x{scan_code:02X}")
            
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 使用NtUserSendInput（更底层的API）
            class KEYBDINPUT(Structure):
                _fields_ = [
                    ("wVk", wintypes.WORD),
                    ("wScan", wintypes.WORD),
                    ("dwFlags", wintypes.DWORD),
                    ("time", wintypes.DWORD),
                    ("dwExtraInfo", POINTER(wintypes.ULONG))
                ]
            
            class INPUT_UNION(Union):
                _fields_ = [("ki", KEYBDINPUT)]
            
            class INPUT(Structure):
                _fields_ = [
                    ("type", wintypes.DWORD),
                    ("ii", INPUT_UNION)
                ]
            
            # 创建输入结构
            inputs = (INPUT * 2)()
            
            # 按下
            inputs[0].type = 1  # INPUT_KEYBOARD
            inputs[0].ii.ki.wVk = 0
            inputs[0].ii.ki.wScan = scan_code
            inputs[0].ii.ki.dwFlags = 0x0008  # KEYEVENTF_SCANCODE
            inputs[0].ii.ki.time = 0
            inputs[0].ii.ki.dwExtraInfo = None
            
            # 释放
            inputs[1].type = 1
            inputs[1].ii.ki.wVk = 0
            inputs[1].ii.ki.wScan = scan_code
            inputs[1].ii.ki.dwFlags = 0x0008 | 0x0002  # KEYEVENTF_SCANCODE | KEYEVENTF_KEYUP
            inputs[1].ii.ki.time = 0
            inputs[1].ii.ki.dwExtraInfo = None
            
            # 尝试使用更底层的API
            try:
                # 方法A: 标准SendInput
                result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
                if result == 2:
                    return True
            except:
                pass
            
            # 方法B: 直接调用ntdll
            try:
                if hasattr(self.ntdll, 'NtUserSendInput'):
                    result = self.ntdll.NtUserSendInput(2, inputs, ctypes.sizeof(INPUT))
                    return result == 2
            except:
                pass
            
            return False
            
        except Exception as e:
            print(f"驱动级输入失败: {e}")
            return False
    
    def method4_process_injection(self, scan_code):
        """方法4: 进程注入（高级方法）"""
        if not self.game_pid:
            return False
        
        try:
            print(f"方法4 - 进程注入: 扫描码 0x{scan_code:02X}")
            
            # 打开目标进程
            PROCESS_ALL_ACCESS = 0x1F0FFF
            process_handle = self.kernel32.OpenProcess(
                PROCESS_ALL_ACCESS, False, self.game_pid
            )
            
            if not process_handle:
                return False
            
            # 在目标进程中分配内存
            MEM_COMMIT = 0x1000
            MEM_RESERVE = 0x2000
            PAGE_EXECUTE_READWRITE = 0x40
            
            # 简单的shellcode，模拟按键
            # 这里只是示例，实际需要更复杂的shellcode
            shellcode = bytes([
                0x50,  # push eax
                0x51,  # push ecx
                0x52,  # push edx
                # ... 这里应该是调用键盘输入的汇编代码
                0x5A,  # pop edx
                0x59,  # pop ecx
                0x58,  # pop eax
                0xC3   # ret
            ])
            
            # 分配内存
            remote_memory = self.kernel32.VirtualAllocEx(
                process_handle, None, len(shellcode),
                MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE
            )
            
            if remote_memory:
                # 写入shellcode
                bytes_written = ctypes.c_size_t()
                self.kernel32.WriteProcessMemory(
                    process_handle, remote_memory, shellcode, len(shellcode),
                    byref(bytes_written)
                )
                
                # 创建远程线程执行
                thread_id = wintypes.DWORD()
                thread_handle = self.kernel32.CreateRemoteThread(
                    process_handle, None, 0, remote_memory, None, 0, byref(thread_id)
                )
                
                if thread_handle:
                    # 等待执行完成
                    self.kernel32.WaitForSingleObject(thread_handle, 1000)
                    self.kernel32.CloseHandle(thread_handle)
                
                # 清理内存
                self.kernel32.VirtualFreeEx(process_handle, remote_memory, 0, 0x8000)
            
            self.kernel32.CloseHandle(process_handle)
            return True
            
        except Exception as e:
            print(f"进程注入失败: {e}")
            return False
    
    def send_key(self, key):
        """发送按键"""
        scan_code = HARDWARE_SCAN_CODES.get(key.lower())
        if not scan_code:
            print(f"未知按键: {key}")
            return False
        
        if not self.game_hwnd:
            print("未找到游戏窗口")
            return False
        
        # 尝试所有方法
        methods = [
            ("硬件级模拟", self.method2_hardware_simulation),
            ("驱动级输入", self.method3_driver_level_input),
            ("直接内存写入", self.method1_direct_memory_write),
            ("进程注入", self.method4_process_injection),
        ]
        
        for name, method in methods:
            try:
                if method(scan_code):
                    print(f"✓ {name} 成功")
                    return True
                else:
                    print(f"✗ {name} 失败")
            except Exception as e:
                print(f"✗ {name} 异常: {e}")
        
        return False

def main():
    """主函数"""
    print("DNF驱动级输入测试")
    print("=" * 40)
    print("基于成功DNF项目的驱动级输入方法")
    print()
    
    # 检查管理员权限
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            print("⚠ 警告: 建议以管理员权限运行")
    except:
        pass
    
    driver_input = DNFDriverInput()
    
    if not driver_input.find_dnf_window():
        print("错误: 未找到DNF窗口")
        print("请确保DNF游戏已启动")
        return
    
    while True:
        print("\n请选择测试:")
        print("1. 测试方向键 (right)")
        print("2. 测试技能键 (e)")
        print("3. 测试空格键")
        print("4. 测试ESC键")
        print("5. 自定义按键")
        print("6. 连续移动测试")
        print("0. 退出")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            print("\n测试方向键 'right'...")
            driver_input.send_key('right')
        elif choice == '2':
            print("\n测试技能键 'e'...")
            driver_input.send_key('e')
        elif choice == '3':
            print("\n测试空格键...")
            driver_input.send_key('space')
        elif choice == '4':
            print("\n测试ESC键...")
            driver_input.send_key('esc')
        elif choice == '5':
            key = input("请输入按键名称: ").strip()
            if key:
                driver_input.send_key(key)
        elif choice == '6':
            print("\n连续移动测试...")
            for i in range(5):
                print(f"移动 {i+1}/5")
                driver_input.send_key('right')
                time.sleep(0.5)
        else:
            print("无效选择")
    
    print("测试结束")

if __name__ == "__main__":
    main()
