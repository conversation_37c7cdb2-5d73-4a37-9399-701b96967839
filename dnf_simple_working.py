#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF简化工作版本
直接使用之前验证有效的方法，不做任何修改
"""

import ctypes
import time
import os
import configparser
from ctypes import wintypes, Structure, Union, POINTER, byref

# 扫描码（与之前测试版本完全一致）
HARDWARE_SCAN_CODES = {
    'a': 0x1E, 'b': 0x30, 'c': 0x2E, 'd': 0x20, 'e': 0x12, 'f': 0x21,
    'g': 0x22, 'h': 0x23, 'i': 0x17, 'j': 0x24, 'k': 0x25, 'l': 0x26,
    'm': 0x32, 'n': 0x31, 'o': 0x18, 'p': 0x19, 'q': 0x10, 'r': 0x13,
    's': 0x1F, 't': 0x14, 'u': 0x16, 'v': 0x2F, 'w': 0x11, 'x': 0x2D,
    'y': 0x15, 'z': 0x2C,
    '0': 0x0B, '1': 0x02, '2': 0x03, '3': 0x04, '4': 0x05,
    '5': 0x06, '6': 0x07, '7': 0x08, '8': 0x09, '9': 0x0A,
    'space': 0x39, 'enter': 0x1C, 'esc': 0x01, 'tab': 0x0F,
    'shift': 0x2A, 'ctrl': 0x1D, 'alt': 0x38,
    'left': 0xCB, 'up': 0xC8, 'right': 0xCD, 'down': 0xD0,
    'f1': 0x3B, 'f2': 0x3C, 'f3': 0x3D, 'f4': 0x3E, 'f5': 0x3F,
}

class SimpleWorkingBot:
    """简化工作版机器人 - 使用之前验证有效的方法"""
    
    def __init__(self):
        self.kernel32 = ctypes.windll.kernel32
        self.user32 = ctypes.windll.user32
        self.ntdll = ctypes.windll.ntdll
        self.game_hwnd = None
        self.game_pid = None
        
        # 配置
        self.skill_key = 'e'
        self.load_config()
        
        # 启用调试权限（与测试版本一致）
        self.enable_debug_privilege()
    
    def load_config(self):
        """加载配置"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            print(f"技能键: {self.skill_key}")
    
    def enable_debug_privilege(self):
        """启用调试权限（与测试版本完全一致）"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if not ctypes.windll.advapi32.OpenProcessToken(
                process, 0x0020 | 0x0008, byref(token)
            ):
                return False
            
            luid = wintypes.LARGE_INTEGER()
            if not ctypes.windll.advapi32.LookupPrivilegeValueW(
                None, "SeDebugPrivilege", byref(luid)
            ):
                return False
            
            class TOKEN_PRIVILEGES(Structure):
                _fields_ = [
                    ("PrivilegeCount", wintypes.DWORD),
                    ("Luid", wintypes.LARGE_INTEGER),
                    ("Attributes", wintypes.DWORD),
                ]
            
            tp = TOKEN_PRIVILEGES()
            tp.PrivilegeCount = 1
            tp.Luid = luid
            tp.Attributes = 0x00000002
            
            result = ctypes.windll.advapi32.AdjustTokenPrivileges(
                token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
            )
            
            ctypes.windll.kernel32.CloseHandle(token)
            print("✓ 调试权限已启用" if result else "✗ 调试权限启用失败")
            return bool(result)
            
        except Exception as e:
            print(f"启用调试权限失败: {e}")
            return False
    
    def find_dnf_window(self):
        """查找DNF窗口（与测试版本完全一致）"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or 
                        "Fighter" in window_title or
                        "地下城与勇士" in window_title):
                        
                        pid = wintypes.DWORD()
                        self.user32.GetWindowThreadProcessId(hwnd, byref(pid))
                        
                        self.game_hwnd = hwnd
                        self.game_pid = pid.value
                        print(f"找到DNF窗口: {window_title}")
                        print(f"窗口句柄: {hwnd}, 进程ID: {self.game_pid}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def method3_driver_level_input(self, scan_code):
        """方法3: 驱动级输入（与测试版本完全一致）"""
        try:
            print(f"驱动级输入: 扫描码 0x{scan_code:02X}")
            
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 使用NtUserSendInput（更底层的API）
            class KEYBDINPUT(Structure):
                _fields_ = [
                    ("wVk", wintypes.WORD),
                    ("wScan", wintypes.WORD),
                    ("dwFlags", wintypes.DWORD),
                    ("time", wintypes.DWORD),
                    ("dwExtraInfo", POINTER(wintypes.ULONG))
                ]
            
            class INPUT_UNION(Union):
                _fields_ = [("ki", KEYBDINPUT)]
            
            class INPUT(Structure):
                _fields_ = [
                    ("type", wintypes.DWORD),
                    ("ii", INPUT_UNION)
                ]
            
            # 创建输入结构
            inputs = (INPUT * 2)()
            
            # 按下
            inputs[0].type = 1  # INPUT_KEYBOARD
            inputs[0].ii.ki.wVk = 0
            inputs[0].ii.ki.wScan = scan_code
            inputs[0].ii.ki.dwFlags = 0x0008  # KEYEVENTF_SCANCODE
            inputs[0].ii.ki.time = 0
            inputs[0].ii.ki.dwExtraInfo = None
            
            # 释放
            inputs[1].type = 1
            inputs[1].ii.ki.wVk = 0
            inputs[1].ii.ki.wScan = scan_code
            inputs[1].ii.ki.dwFlags = 0x0008 | 0x0002  # KEYEVENTF_SCANCODE | KEYEVENTF_KEYUP
            inputs[1].ii.ki.time = 0
            inputs[1].ii.ki.dwExtraInfo = None
            
            # 尝试使用更底层的API
            try:
                # 方法A: 标准SendInput
                result = self.user32.SendInput(2, inputs, ctypes.sizeof(INPUT))
                if result == 2:
                    return True
            except:
                pass
            
            # 方法B: 直接调用ntdll
            try:
                if hasattr(self.ntdll, 'NtUserSendInput'):
                    result = self.ntdll.NtUserSendInput(2, inputs, ctypes.sizeof(INPUT))
                    return result == 2
            except:
                pass
            
            return False
            
        except Exception as e:
            print(f"驱动级输入失败: {e}")
            return False
    
    def method2_hardware_simulation(self, scan_code):
        """方法2: 硬件级模拟（与测试版本完全一致）"""
        try:
            print(f"硬件级模拟: 扫描码 0x{scan_code:02X}")
            
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 使用最底层的键盘端口模拟
            # 发送扫描码到键盘缓冲区
            # 按下
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # KEYEVENTF_SCANCODE
            time.sleep(0.05)
            # 释放
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # KEYEVENTF_SCANCODE | KEYEVENTF_KEYUP
            
            return True
            
        except Exception as e:
            print(f"硬件级模拟失败: {e}")
            return False
    
    def send_key(self, key):
        """发送按键（使用之前验证有效的方法）"""
        scan_code = HARDWARE_SCAN_CODES.get(key.lower())
        if not scan_code:
            print(f"未知按键: {key}")
            return False
        
        if not self.game_hwnd:
            print("未找到游戏窗口")
            return False
        
        # 使用之前测试有效的方法
        methods = [
            ("硬件级模拟", self.method2_hardware_simulation),
            ("驱动级输入", self.method3_driver_level_input),
        ]
        
        for name, method in methods:
            try:
                if method(scan_code):
                    print(f"✓ {name} 成功")
                    return True
                else:
                    print(f"✗ {name} 失败")
            except Exception as e:
                print(f"✗ {name} 异常: {e}")
        
        return False
    
    def test_movement(self):
        """测试移动"""
        print("测试角色移动...")
        print("请观察角色是否向右移动")
        
        for i in range(3):
            print(f"移动测试 {i+1}/3")
            self.send_key('right')
            time.sleep(1)
    
    def simple_farming_cycle(self):
        """简化的搬砖循环"""
        print("\n=== 简化搬砖循环 ===")
        
        # 1. 进入地图
        print("1. 进入地图选择")
        self.send_key('right')
        time.sleep(2)
        
        print("2. 确认进入")
        self.send_key('space')
        time.sleep(3)
        
        # 3. 简化的打怪循环
        for room in range(3):  # 只测试3个房间
            print(f"3.{room+1} 清理房间 {room+1}")
            
            # 移动
            print("  - 向右移动")
            self.send_key('right')
            time.sleep(0.5)
            self.send_key('right')
            time.sleep(0.5)
            
            # 攻击
            print(f"  - 释放技能 {self.skill_key}")
            for _ in range(3):
                self.send_key(self.skill_key)
                time.sleep(0.3)
            
            # 等待
            time.sleep(1)
            
            # 顺图（简化）
            if room < 2:
                print("  - 顺图")
                # 先按Alt
                self.send_key('alt')
                time.sleep(0.05)
                self.send_key('right')
                time.sleep(1.5)
        
        # 4. 退出
        print("4. 退出地图")
        for i in range(2):
            print(f"  - ESC {i+1}")
            self.send_key('esc')
            time.sleep(1)
        
        print("=== 简化搬砖循环完成 ===")

def main():
    """主函数"""
    print("DNF简化工作版本")
    print("=" * 30)
    print("使用之前验证有效的驱动级输入方法")
    print()
    
    bot = SimpleWorkingBot()
    
    if not bot.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    while True:
        print("\n请选择:")
        print("1. 测试移动")
        print("2. 测试技能")
        print("3. 简化搬砖循环")
        print("0. 退出")
        
        choice = input("\n请选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            bot.test_movement()
        elif choice == '2':
            print("测试技能释放...")
            for i in range(3):
                print(f"技能 {i+1}/3")
                bot.send_key(bot.skill_key)
                time.sleep(0.5)
        elif choice == '3':
            print("执行简化搬砖循环...")
            bot.simple_farming_cycle()
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
