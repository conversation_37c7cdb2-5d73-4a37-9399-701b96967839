#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快捷键管理模块
负责处理所有快捷键功能
"""

import time
import threading
import logging
from pynput import keyboard
from typing import Dict, Callable

class HotkeyManager:
    """快捷键管理器"""
    
    def __init__(self, game_controller):
        self.game_controller = game_controller
        self.logger = logging.getLogger("HotkeyManager")
        self.listener = None
        self.running = False
        
        # 快捷键状态
        self.auto_pickup_enabled = False
        
        # 当前按下的修饰键
        self.pressed_keys = set()
        
        # 快捷键映射
        self.hotkey_actions = {
            frozenset([keyboard.Key.alt_l, keyboard.KeyCode.from_char('q')]): self.toggle_auto_pickup,
            frozenset([keyboard.Key.alt_l, keyboard.KeyCode.from_char('w')]): self.quick_repair,
            frozenset([keyboard.Key.alt_l, keyboard.KeyCode.from_char('d')]): self.direct_decompose,
            frozenset([keyboard.Key.alt_l, keyboard.Key.right]): self.next_room,
            frozenset([keyboard.KeyCode.from_char('0')]): self.stall_decompose,
            frozenset([keyboard.KeyCode.from_char('i')]): self.open_bag,
            frozenset([keyboard.KeyCode.from_char('4')]): self.number_4_key,
        }
    
    def start(self):
        """启动快捷键监听"""
        if self.running:
            return
        
        self.running = True
        self.listener = keyboard.Listener(
            on_press=self._on_key_press,
            on_release=self._on_key_release
        )
        self.listener.start()
        self.logger.info("快捷键监听已启动")
    
    def stop(self):
        """停止快捷键监听"""
        self.running = False
        if self.listener:
            self.listener.stop()
            self.listener = None
        self.logger.info("快捷键监听已停止")
    
    def _on_key_press(self, key):
        """按键按下事件"""
        if not self.running:
            return
        
        self.pressed_keys.add(key)
        
        # 检查是否匹配任何快捷键组合
        for hotkey_combo, action in self.hotkey_actions.items():
            if hotkey_combo.issubset(self.pressed_keys):
                try:
                    # 在新线程中执行动作，避免阻塞键盘监听
                    threading.Thread(target=action, daemon=True).start()
                except Exception as e:
                    self.logger.error(f"执行快捷键动作失败: {e}")
    
    def _on_key_release(self, key):
        """按键释放事件"""
        if not self.running:
            return
        
        self.pressed_keys.discard(key)
    
    def toggle_auto_pickup(self):
        """切换自动入包状态 (Alt+Q)"""
        self.auto_pickup_enabled = not self.auto_pickup_enabled
        status = "开启" if self.auto_pickup_enabled else "关闭"
        self.logger.info(f"自动入包已{status}")
        
        # 这里可以添加实际的自动入包逻辑
        # 比如调用游戏控制器的相关方法
    
    def quick_repair(self):
        """快速修理装备 (Alt+W)"""
        self.logger.info("执行快速修理装备")
        try:
            # 调用游戏控制器的修理方法
            self.game_controller.quick_repair_equipment()
        except Exception as e:
            self.logger.error(f"快速修理失败: {e}")
    
    def direct_decompose(self):
        """直接分解 (Alt+D)"""
        self.logger.info("执行直接分解")
        try:
            # 调用游戏控制器的分解方法
            self.game_controller.direct_decompose()
        except Exception as e:
            self.logger.error(f"直接分解失败: {e}")
    
    def next_room(self):
        """顺图到下一个房间 (Alt+Right)"""
        self.logger.info("执行顺图")
        try:
            # 调用游戏控制器的顺图方法
            self.game_controller.move_to_next_room()
        except Exception as e:
            self.logger.error(f"顺图失败: {e}")
    
    def stall_decompose(self):
        """摆摊分解机 (0)"""
        self.logger.info("执行摆摊分解")
        try:
            # 调用游戏控制器的摆摊分解方法
            self.game_controller.stall_decompose()
        except Exception as e:
            self.logger.error(f"摆摊分解失败: {e}")
    
    def open_bag(self):
        """打开背包 (I)"""
        self.logger.info("打开背包")
        try:
            # 调用游戏控制器的打开背包方法
            self.game_controller.open_bag()
        except Exception as e:
            self.logger.error(f"打开背包失败: {e}")
    
    def number_4_key(self):
        """数字键4"""
        self.logger.info("按下数字键4")
        try:
            # 调用游戏控制器的相关方法
            self.game_controller.press_number_4()
        except Exception as e:
            self.logger.error(f"按数字键4失败: {e}")
    
    def is_auto_pickup_enabled(self) -> bool:
        """获取自动入包状态"""
        return self.auto_pickup_enabled
