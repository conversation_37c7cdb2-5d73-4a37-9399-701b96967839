#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 不需要复杂依赖
测试基础功能是否正常
"""

import os
import sys
import time

def test_basic_functions():
    """测试基础功能"""
    print("=" * 50)
    print("DNF搬砖脚本 - 快速测试")
    print("=" * 50)
    
    # 测试Python版本
    print(f"Python版本: {sys.version}")
    
    # 测试项目结构
    print("\n检查项目文件:")
    files_to_check = [
        "main.py",
        "config.ini", 
        "modules/config_manager.py",
        "modules/input_simulator.py",
        "modules/image_detector.py",
        "modules/game_controller.py"
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file}")
    
    # 测试配置文件读取
    print("\n测试配置文件读取:")
    try:
        import configparser
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        work_hours = config.get('GENERAL', 'work_hours', fallback='8')
        skill_key = config.get('GENERAL', 'skill_key', fallback='e')
        
        print(f"✓ 工作时长: {work_hours}小时")
        print(f"✓ 技能键: {skill_key}")
        
    except Exception as e:
        print(f"✗ 配置文件读取失败: {e}")
    
    # 测试Windows API
    print("\n测试Windows API:")
    try:
        import win32gui
        import win32api
        print("✓ Windows API可用")
        
        # 测试查找窗口
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if ("地下城与勇士" in window_text or
                    "DNF" in window_text or
                    "Dungeon" in window_text or
                    "Fighter" in window_text):
                    windows.append((hwnd, window_text))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            print(f"✓ 找到DNF窗口: {windows[0][1]}")
        else:
            print("⚠ 未找到DNF窗口（请确保游戏已启动）")
            
    except ImportError:
        print("✗ Windows API不可用，需要安装pywin32")
    except Exception as e:
        print(f"✗ Windows API测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)

def show_next_steps():
    """显示下一步操作"""
    print("\n📋 下一步操作指南:")
    print("1. 等待依赖包安装完成")
    print("2. 启动DNF游戏并登录角色")
    print("3. 确保角色在城镇中")
    print("4. 运行: python main.py")
    print("\n⚙️ 配置说明:")
    print("- 编辑 config.ini 调整设置")
    print("- 在 images/ 目录放置模板图像")
    print("- 查看 README.md 了解详细使用方法")
    print("\n🔧 如果遇到问题:")
    print("- 运行: python test_modules.py")
    print("- 检查日志文件: logs/")
    print("- 确保以管理员权限运行")

if __name__ == "__main__":
    test_basic_functions()
    show_next_steps()
    
    input("\n按回车键退出...")
