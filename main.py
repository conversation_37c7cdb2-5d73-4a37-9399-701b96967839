#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF格兰迪搬砖自动化脚本
主程序入口
"""

import sys
import os
import time
import logging
from datetime import datetime, timedelta
import configparser
import threading
import signal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.config_manager import ConfigManager
from modules.input_simulator import InputSimulator
from modules.image_detector import ImageDetector
from modules.game_controller import GameController
from modules.hotkey_manager import HotkeyManager
from modules.logger import setup_logger

class DNFBot:
    """DNF格兰迪搬砖机器人主类"""
    
    def __init__(self):
        self.config = ConfigManager()
        self.logger = setup_logger()
        self.input_sim = InputSimulator()
        self.image_detector = ImageDetector()
        self.game_controller = GameController(self.input_sim, self.image_detector, self.config)
        self.hotkey_manager = HotkeyManager(self.game_controller)
        
        self.running = False
        self.start_time = None
        self.work_duration = self.config.get_work_hours() * 3600  # 转换为秒
        
    def start(self):
        """启动机器人"""
        self.logger.info("DNF格兰迪搬砖机器人启动")
        self.logger.info(f"工作时长设置: {self.config.get_work_hours()}小时")
        
        # 启动热键监听
        self.hotkey_manager.start()
        
        self.running = True
        self.start_time = datetime.now()
        
        try:
            self.main_loop()
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"程序异常: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止机器人"""
        self.running = False
        self.hotkey_manager.stop()
        self.logger.info("DNF格兰迪搬砖机器人已停止")
    
    def main_loop(self):
        """主循环"""
        while self.running:
            # 检查是否超过工作时长
            if self.is_work_time_exceeded():
                self.logger.info("已达到设定工作时长，程序结束")
                break
            
            try:
                # 执行主要游戏逻辑
                self.game_controller.execute_main_logic()
                
            except Exception as e:
                self.logger.error(f"主循环执行错误: {e}")
                time.sleep(5)  # 错误后等待5秒再继续
    
    def is_work_time_exceeded(self):
        """检查是否超过工作时长"""
        if self.start_time is None:
            return False
        
        elapsed = (datetime.now() - self.start_time).total_seconds()
        return elapsed >= self.work_duration

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n接收到退出信号，正在安全退出...")
    sys.exit(0)

def main():
    """主函数"""
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建必要的目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("images", exist_ok=True)
    
    # 启动机器人
    bot = DNFBot()
    bot.start()

if __name__ == "__main__":
    main()
