#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF简单门识别 - 使用模板匹配
"""

import ctypes
import time
import os
import cv2
import numpy as np
from PIL import ImageGrab
import logging
from datetime import datetime
from ctypes import wintypes, byref

class SimpleDoorBot:
    """简单的门识别机器人"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.game_hwnd = None
        self.window_rect = None
        
        # 配置
        self.skill_key = 'e'
        
        # 门模板
        self.normal_door_template = None
        self.boss_door_template = None
        
        # 初始化
        self.setup_logging()
        self.enable_debug_privilege()
        self.load_door_templates()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def enable_debug_privilege(self):
        """启用调试权限"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                self.logger.info("调试权限已启用")
        except Exception as e:
            self.logger.warning(f"调试权限启用失败: {e}")
    
    def load_door_templates(self):
        """加载门模板"""
        # 加载普通门模板
        if os.path.exists("normal_door.png"):
            self.normal_door_template = cv2.imread("normal_door.png")
            self.logger.info("已加载普通门模板")
        else:
            self.logger.warning("未找到普通门模板: normal_door.png")
        
        # 加载BOSS门模板
        if os.path.exists("boss_door.png"):
            self.boss_door_template = cv2.imread("boss_door.png")
            self.logger.info("已加载BOSS门模板")
        else:
            self.logger.warning("未找到BOSS门模板: boss_door.png")
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        self.logger.info(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def capture_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            screen = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            return screen
        except Exception as e:
            self.logger.error(f"截屏失败: {e}")
            return None
    
    def send_key(self, key):
        """发送按键"""
        scan_codes = {
            'e': 0x12, 'space': 0x39, 'esc': 0x01,
            'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0,
            'alt': 0x38
        }
        
        scan_code = scan_codes.get(key.lower())
        if not scan_code:
            return False
        
        # 激活窗口
        self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)
        
        try:
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            return True
        except Exception as e:
            self.logger.error(f"发送按键失败: {e}")
            return False
    
    def find_door(self):
        """寻找门（普通门或BOSS门）"""
        screen = self.capture_screen()
        if screen is None:
            return None
        
        doors = []
        
        # 寻找普通门
        if self.normal_door_template is not None:
            result = cv2.matchTemplate(screen, self.normal_door_template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val > 0.7:  # 匹配阈值
                h, w = self.normal_door_template.shape[:2]
                door_x = max_loc[0] + w // 2
                door_y = max_loc[1] + h // 2
                doors.append((door_x, door_y, "普通门", max_val))
                self.logger.info(f"找到普通门: 位置({door_x}, {door_y}), 匹配度{max_val:.3f}")
        
        # 寻找BOSS门
        if self.boss_door_template is not None:
            result = cv2.matchTemplate(screen, self.boss_door_template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val > 0.7:  # 匹配阈值
                h, w = self.boss_door_template.shape[:2]
                door_x = max_loc[0] + w // 2
                door_y = max_loc[1] + h // 2
                doors.append((door_x, door_y, "BOSS门", max_val))
                self.logger.info(f"找到BOSS门: 位置({door_x}, {door_y}), 匹配度{max_val:.3f}")
        
        # 返回匹配度最高的门
        if doors:
            best_door = max(doors, key=lambda x: x[3])
            return best_door[:3]  # 返回 (x, y, type)
        
        return None
    
    def find_and_move_to_door(self):
        """寻找门并移动到门口"""
        self.logger.info("开始寻找门")
        
        # 最多尝试10次，每次向右移动寻找
        for attempt in range(10):
            door_info = self.find_door()
            
            if door_info:
                door_x, door_y, door_type = door_info
                self.logger.info(f"找到{door_type}: 位置({door_x}, {door_y})")
                
                # 移动到门口
                screen = self.capture_screen()
                if screen is None:
                    return False
                
                h, w = screen.shape[:2]
                char_x = w // 2  # 假设角色在画面中央
                
                # 计算移动距离
                if door_x > char_x + 50:  # 门在右边
                    move_steps = min(5, (door_x - char_x) // 80)
                    self.logger.info(f"向右移动 {move_steps} 步到达门口")
                    
                    for _ in range(move_steps):
                        self.send_key('right')
                        time.sleep(0.2)
                
                self.logger.info("已到达门口")
                return True
            else:
                # 当前画面没有门，继续向右寻找
                self.logger.debug(f"第{attempt+1}次未找到门，向右移动寻找")
                for _ in range(3):
                    self.send_key('right')
                    time.sleep(0.15)
        
        self.logger.warning("未找到门")
        return False
    
    def clear_room(self):
        """清理房间"""
        self.logger.info("开始清理房间")
        
        # 向右移动并释放技能
        for _ in range(8):
            self.send_key('right')
            time.sleep(0.1)
        
        # 释放技能
        for _ in range(12):
            self.send_key(self.skill_key)
            time.sleep(0.2)
        
        time.sleep(2)  # 等待怪物死亡
        self.logger.info("房间清理完成")
    
    def move_to_next_room(self):
        """移动到下一房间"""
        self.logger.info("准备移动到下一房间")
        
        # 寻找并移动到门口
        if self.find_and_move_to_door():
            # 尝试进入门
            self.logger.info("尝试进入门")
            
            # 直接向右走进门
            for _ in range(4):
                self.send_key('right')
                time.sleep(0.2)
            
            self.logger.info("已进入下一房间")
            return True
        else:
            self.logger.warning("未找到门，无法进入下一房间")
            return False
    
    def test_door_detection(self):
        """测试门识别"""
        self.logger.info("测试门识别功能")
        
        door_info = self.find_door()
        if door_info:
            door_x, door_y, door_type = door_info
            print(f"✓ 找到{door_type}: 位置({door_x}, {door_y})")
            
            choice = input("是否移动到门口? (y/n): ").strip().lower()
            if choice == 'y':
                self.find_and_move_to_door()
        else:
            print("✗ 未找到门")
            print("请确保:")
            print("1. 有 normal_door.png 和 boss_door.png 模板文件")
            print("2. 角色在地下城房间中")
            print("3. 画面中有可见的门")

def main():
    """主函数"""
    print("DNF简单门识别机器人")
    print("=" * 30)
    print("使用模板匹配识别门")
    print()
    print("需要的文件:")
    print("- normal_door.png: 普通门的图片")
    print("- boss_door.png: BOSS门的图片")
    print()
    
    bot = SimpleDoorBot()
    
    if not bot.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    while True:
        print("\n请选择操作:")
        print("1. 测试门识别")
        print("2. 测试移动到门")
        print("3. 截取当前画面")
        print("0. 退出")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            bot.test_door_detection()
        elif choice == '2':
            if bot.find_and_move_to_door():
                print("✓ 成功移动到门口")
            else:
                print("✗ 移动失败")
        elif choice == '3':
            screen = bot.capture_screen()
            if screen is not None:
                cv2.imwrite("current_screen.png", screen)
                print("当前画面已保存: current_screen.png")
                print("您可以从这个截图中裁剪出门的图片")
                print("保存为 normal_door.png 和 boss_door.png")
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
