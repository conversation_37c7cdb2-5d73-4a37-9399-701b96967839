#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按键测试工具
用于测试向DNF窗口发送按键是否有效
"""

import ctypes
import time

# 虚拟键码
VK_CODE = {
    'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46,
    'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A, 'k': 0x4B, 'l': 0x4C,
    'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
    's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58,
    'y': 0x59, 'z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
    '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
    'space': 0x20, 'enter': 0x0D, 'esc': 0x1B, 'tab': 0x09,
    'shift': 0x10, 'ctrl': 0x11, 'alt': 0x12,
    'left': 0x25, 'up': 0x26, 'right': 0x27, 'down': 0x28,
}

class KeyTester:
    """按键测试器"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or 
                        "Fighter" in window_title or
                        "地下城与勇士" in window_title):
                        self.game_hwnd = hwnd
                        print(f"找到DNF窗口: {window_title} (句柄: {hwnd})")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def activate_window(self):
        """激活窗口"""
        if self.game_hwnd:
            self.user32.SetForegroundWindow(self.game_hwnd)
            self.user32.ShowWindow(self.game_hwnd, 9)
            time.sleep(0.2)
            return True
        return False
    
    def send_key_message(self, key):
        """发送按键消息到窗口"""
        if not self.game_hwnd:
            print("错误: 未找到游戏窗口")
            return False
        
        vk_code = VK_CODE.get(key.lower())
        if vk_code is None:
            print(f"错误: 未知按键 {key}")
            return False
        
        try:
            # 方法1: PostMessage
            WM_KEYDOWN = 0x0100
            WM_KEYUP = 0x0101
            
            scan_code = self.user32.MapVirtualKeyW(vk_code, 0)
            lParam_down = (scan_code << 16) | 1
            lParam_up = (scan_code << 16) | 0xC0000001
            
            print(f"发送按键 '{key}' (VK:{vk_code}) 到窗口 {self.game_hwnd}")
            
            result1 = self.user32.PostMessageW(self.game_hwnd, WM_KEYDOWN, vk_code, lParam_down)
            time.sleep(0.05)
            result2 = self.user32.PostMessageW(self.game_hwnd, WM_KEYUP, vk_code, lParam_up)
            
            print(f"PostMessage结果: KeyDown={result1}, KeyUp={result2}")
            return True
            
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False
    
    def send_key_global(self, key):
        """发送全局按键"""
        vk_code = VK_CODE.get(key.lower())
        if vk_code is None:
            print(f"错误: 未知按键 {key}")
            return False
        
        try:
            print(f"发送全局按键 '{key}' (VK:{vk_code})")
            
            # 激活窗口
            self.activate_window()
            
            # 全局按键
            self.user32.keybd_event(vk_code, 0, 0, 0)
            time.sleep(0.05)
            self.user32.keybd_event(vk_code, 0, 2, 0)  # KEYEVENTF_KEYUP
            
            print("全局按键发送完成")
            return True
            
        except Exception as e:
            print(f"发送全局按键失败: {e}")
            return False

def main():
    """主函数"""
    print("DNF按键测试工具")
    print("=" * 30)
    
    tester = KeyTester()
    
    if not tester.find_dnf_window():
        print("错误: 未找到DNF窗口")
        print("请确保:")
        print("1. DNF游戏已启动")
        print("2. 游戏窗口可见（未最小化）")
        return
    
    print(f"成功找到DNF窗口，句柄: {tester.game_hwnd}")
    print()
    
    while True:
        print("\n请选择测试方式:")
        print("1. 发送消息到窗口 (PostMessage)")
        print("2. 发送全局按键 (keybd_event)")
        print("3. 激活窗口")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            key = input("请输入要测试的按键 (如: e, space, right): ").strip()
            if key:
                tester.send_key_message(key)
        elif choice == '2':
            key = input("请输入要测试的按键 (如: e, space, right): ").strip()
            if key:
                tester.send_key_global(key)
        elif choice == '3':
            if tester.activate_window():
                print("窗口激活成功")
            else:
                print("窗口激活失败")
        else:
            print("无效选择")
    
    print("测试结束")

if __name__ == "__main__":
    main()
