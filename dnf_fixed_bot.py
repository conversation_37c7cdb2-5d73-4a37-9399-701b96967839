#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNF修复版机器人
使用验证有效的按键方法
"""

import ctypes
import time
import os
import cv2
import numpy as np
from PIL import ImageGrab
import configparser
import logging
from datetime import datetime
from ctypes import wintypes, byref

# 扫描码（与测试工具完全一致）
HARDWARE_SCAN_CODES = {
    'e': 0x12, 'space': 0x39, 'esc': 0x01, '4': 0x05,
    'right': 0xCD, 'left': 0xCB, 'up': 0xC8, 'down': 0xD0,
    'alt': 0x38, 'f11': 0x57, '0': 0x0B,
    'a': 0x1E, 's': 0x1F, 'd': 0x20, 'w': 0x11,
}

class DNFFixedBot:
    """DNF修复版机器人"""
    
    def __init__(self):
        self.kernel32 = ctypes.windll.kernel32
        self.user32 = ctypes.windll.user32
        self.game_hwnd = None
        self.window_rect = None
        
        # 配置
        self.skill_key = 'e'
        self.enable_abyss = False
        
        # 模板缓存
        self.templates = {}

        # 学习到的方向序列
        self.grandi_directions = []

        # 初始化
        self.setup_logging()
        self.load_config()
        self.enable_debug_privilege()
        self.load_templates()
        self.load_directions()
    
    def setup_logging(self):
        """设置日志"""
        os.makedirs("logs", exist_ok=True)
        log_file = f"logs/dnf_fixed_bot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """加载配置"""
        if os.path.exists('config.ini'):
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            self.skill_key = config.get('GENERAL', 'skill_key', fallback='e')
            self.enable_abyss = config.getboolean('FEATURES', 'enable_abyss', fallback=False)
    
    def enable_debug_privilege(self):
        """启用调试权限（与测试工具完全一致）"""
        try:
            token = wintypes.HANDLE()
            process = self.kernel32.GetCurrentProcess()
            
            if ctypes.windll.advapi32.OpenProcessToken(process, 0x0028, byref(token)):
                luid = wintypes.LARGE_INTEGER()
                if ctypes.windll.advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", byref(luid)):
                    
                    class TOKEN_PRIVILEGES(ctypes.Structure):
                        _fields_ = [
                            ("PrivilegeCount", wintypes.DWORD),
                            ("Luid", wintypes.LARGE_INTEGER),
                            ("Attributes", wintypes.DWORD),
                        ]
                    
                    tp = TOKEN_PRIVILEGES()
                    tp.PrivilegeCount = 1
                    tp.Luid = luid
                    tp.Attributes = 0x00000002
                    
                    ctypes.windll.advapi32.AdjustTokenPrivileges(
                        token, False, byref(tp), ctypes.sizeof(TOKEN_PRIVILEGES), None, None
                    )
                
                ctypes.windll.kernel32.CloseHandle(token)
                self.logger.info("调试权限已启用")
        except Exception as e:
            self.logger.warning(f"调试权限启用失败: {e}")
    
    def load_templates(self):
        """加载所有模板"""
        template_dir = "templates"
        if not os.path.exists(template_dir):
            self.logger.error("模板目录不存在")
            return
        
        template_files = [f for f in os.listdir(template_dir) if f.endswith('.png')]
        
        for template_file in template_files:
            template_name = template_file[:-4]
            template_path = os.path.join(template_dir, template_file)
            template = cv2.imread(template_path)
            
            if template is not None:
                self.templates[template_name] = template
                self.logger.debug(f"加载模板: {template_name}")
        
        self.logger.info(f"共加载 {len(self.templates)} 个模板")

    def load_directions(self):
        """加载学习到的方向数据"""
        import json

        if os.path.exists('grandi_directions.json'):
            try:
                with open('grandi_directions.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.grandi_directions = data.get('grandi_directions', [])
                    self.logger.info(f"加载了 {len(self.grandi_directions)} 个顺图方向")
            except Exception as e:
                self.logger.warning(f"加载方向数据失败: {e}")
                self.grandi_directions = []
        else:
            self.logger.warning("未找到方向数据文件，将使用默认方向")
            # 默认方向（可能不准确）
            self.grandi_directions = ['right', 'down', 'right', 'down', 'right', 'down', 'right']
    
    def find_dnf_window(self):
        """查找DNF窗口"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value
                    
                    if ("Dungeon" in window_title or "Fighter" in window_title):
                        self.game_hwnd = hwnd
                        
                        # 获取窗口位置
                        rect = wintypes.RECT()
                        self.user32.GetWindowRect(hwnd, byref(rect))
                        self.window_rect = (rect.left, rect.top, rect.right, rect.bottom)
                        
                        self.logger.info(f"找到DNF窗口: {window_title}")
                        return False
            return True
        
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
        self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
        return self.game_hwnd is not None
    
    def send_key(self, key):
        """发送按键（使用验证有效的方法1）"""
        scan_code = HARDWARE_SCAN_CODES.get(key.lower())
        if not scan_code:
            self.logger.error(f"未知按键: {key}")
            return False
        
        # 激活窗口（与测试工具完全一致）
        self.logger.debug(f"激活窗口并发送按键: {key}")
        result = self.user32.SetForegroundWindow(self.game_hwnd)
        self.logger.debug(f"窗口激活结果: {result}")
        time.sleep(0.1)  # 与测试工具一致的等待时间
        
        try:
            # 使用与测试工具完全相同的方法
            self.logger.debug(f"发送扫描码: 0x{scan_code:02X}")
            self.user32.keybd_event(0, scan_code, 0x0008, 0)  # 按下
            time.sleep(0.05)
            self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)  # 释放
            
            self.logger.debug(f"✓ 成功发送按键: {key}")
            return True
        except Exception as e:
            self.logger.error(f"发送按键失败: {e}")
            return False
    
    def capture_game_screen(self):
        """截取游戏画面"""
        if not self.window_rect:
            return None
        
        try:
            # 激活窗口
            self.user32.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.1)
            
            # 截取游戏窗口
            screenshot = ImageGrab.grab(bbox=self.window_rect)
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            self.logger.error(f"截屏失败: {e}")
            return None
    
    def find_template(self, template_name, threshold=0.7):
        """查找模板"""
        if template_name not in self.templates:
            self.logger.warning(f"模板不存在: {template_name}")
            return None
        
        screen = self.capture_game_screen()
        if screen is None:
            return None
        
        template = self.templates[template_name]
        result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        if max_val > threshold:
            h, w = template.shape[:2]
            center_x = max_loc[0] + w // 2
            center_y = max_loc[1] + h // 2
            self.logger.debug(f"找到模板 {template_name}: 匹配度{max_val:.3f}")
            return (center_x, center_y, max_val)
        
        self.logger.debug(f"未找到模板 {template_name}: 最高匹配度{max_val:.3f}")
        return None
    
    def test_movement(self):
        """测试移动功能"""
        self.logger.info("测试角色移动...")
        
        for i in range(3):
            self.logger.info(f"测试移动 {i+1}/3")
            if self.send_key('right'):
                self.logger.info("按键发送成功")
            else:
                self.logger.error("按键发送失败")
            time.sleep(1)
        
        self.logger.info("移动测试完成")
    
    def simple_enter_map(self):
        """简单的进入地图测试"""
        self.logger.info("测试进入地图...")
        
        # 1. 检查是否在城镇
        town_found = self.find_template("town_interface")
        self.logger.info(f"城镇界面检测: {'找到' if town_found else '未找到'}")
        
        # 2. 尝试进入地图选择
        self.logger.info("发送右键进入地图选择...")
        if self.send_key('right'):
            self.logger.info("右键发送成功")
            time.sleep(3)  # 等待界面变化
            
            # 3. 检查是否进入地图选择
            map_selection_found = self.find_template("map_selection_interface")
            self.logger.info(f"地图选择界面检测: {'找到' if map_selection_found else '未找到'}")
            
            if map_selection_found:
                self.logger.info("✓ 成功进入地图选择界面")
                return True
            else:
                self.logger.warning("✗ 未能进入地图选择界面")
                return False
        else:
            self.logger.error("右键发送失败")
            return False

    def send_combination(self, keys):
        """发送组合键"""
        scan_codes = []
        for key in keys:
            scan_code = HARDWARE_SCAN_CODES.get(key.lower())
            if scan_code:
                scan_codes.append(scan_code)
            else:
                self.logger.error(f"未知按键: {key}")
                return False

        # 激活窗口
        result = self.user32.SetForegroundWindow(self.game_hwnd)
        time.sleep(0.1)

        try:
            # 按下所有键
            for scan_code in scan_codes:
                self.user32.keybd_event(0, scan_code, 0x0008, 0)
                time.sleep(0.01)

            time.sleep(0.05)

            # 释放所有键（逆序）
            for scan_code in reversed(scan_codes):
                self.user32.keybd_event(0, scan_code, 0x0008 | 0x0002, 0)
                time.sleep(0.01)

            self.logger.debug(f"✓ 成功发送组合键: {'+'.join(keys)}")
            return True
        except Exception as e:
            self.logger.error(f"发送组合键失败: {e}")
            return False

    def wait_for_template(self, template_name, timeout=10):
        """等待模板出现"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self.find_template(template_name):
                return True
            time.sleep(0.5)

        return False

    def is_room_clear(self):
        """检查房间是否清空"""
        # 检查是否有亮的房间格子（表示可通行）
        if self.find_template("minimap_next_room_bright"):
            return True

        # 检查是否没有暗的房间格子
        if not self.find_template("minimap_next_room_dark"):
            return True

        return False

    def enter_grandi_dungeon(self):
        """进入格兰迪地下城"""
        self.logger.info("开始进入格兰迪地下城")

        # 1. 进入地图选择
        self.logger.info("进入地图选择")
        if not self.send_key('right'):
            return False

        # 等待地图选择界面
        if not self.wait_for_template("map_selection_interface", timeout=5):
            self.logger.error("未能进入地图选择界面")
            return False

        # 2. 等待选择（假设用户已手动选择格兰迪和地狱难度）
        time.sleep(1)

        # 3. 开启深渊模式（如果启用）
        if self.enable_abyss:
            self.logger.info("开启深渊模式")
            self.send_key('f11')
            time.sleep(0.5)

        # 4. 确认进入
        self.logger.info("确认进入地下城")
        if not self.send_key('space'):
            return False

        # 等待进入地下城
        time.sleep(5)  # 等待加载
        self.logger.info("已进入地下城")
        return True

    def clear_room(self):
        """清理当前房间"""
        self.logger.info("开始清理房间")

        # 检查是否是深渊房间
        if self.enable_abyss and self.find_template("abyss_pillar_normal"):
            return self.handle_abyss_room()
        else:
            return self.clear_normal_room()

    def handle_abyss_room(self):
        """处理深渊房间"""
        self.logger.info("检测到深渊房间")

        for wave in range(3):  # 3波
            self.logger.info(f"深渊第 {wave+1}/3 波")

            # 激活柱子
            if self.activate_abyss_pillar():
                # 清理这波怪物
                self.clear_wave_monsters()

                if wave < 2:  # 不是最后一波
                    time.sleep(2)  # 等待下一波
            else:
                self.logger.warning(f"第{wave+1}波柱子激活失败")

        return True

    def activate_abyss_pillar(self):
        """激活深渊柱子"""
        # 寻找正常状态的柱子
        if self.find_template("abyss_pillar_normal"):
            self.logger.info("找到正常柱子，开始攻击")

            # 攻击柱子
            for _ in range(10):
                self.send_key(self.skill_key)
                time.sleep(0.3)

                # 检查是否激活
                if self.find_template("abyss_pillar_activated"):
                    self.logger.info("柱子已激活")
                    return True

        return False

    def clear_wave_monsters(self):
        """清理当前波怪物"""
        max_time = 20
        start_time = time.time()

        while time.time() - start_time < max_time:
            if self.is_room_clear():
                self.logger.info("怪物清理完成")
                return True

            # 攻击
            self.send_key(self.skill_key)
            time.sleep(0.2)

            # 移动
            import random
            move_key = random.choice(['right', 'left', 'up', 'down'])
            self.send_key(move_key)
            time.sleep(0.1)

        return True

    def clear_normal_room(self):
        """清理普通房间"""
        self.logger.info("清理普通房间")

        max_time = 15
        start_time = time.time()

        while time.time() - start_time < max_time:
            if self.is_room_clear():
                self.logger.info("普通房间清理完成")
                return True

            # 攻击和移动
            self.send_key(self.skill_key)
            time.sleep(0.2)
            self.send_key('right')
            time.sleep(0.1)

        return True

    def get_next_room_direction(self, current_room):
        """获取下个房间的方向"""
        # 使用学习到的方向数据
        if current_room < len(self.grandi_directions):
            direction = self.grandi_directions[current_room]
            self.logger.info(f"使用学习到的方向: 房间{current_room+1} → 房间{current_room+2} = {direction}")
            return direction
        else:
            # 如果没有学习数据，使用默认方向
            self.logger.warning(f"没有房间{current_room+1}的方向数据，使用默认方向")
            return 'right'

    def move_to_next_room(self, current_room):
        """移动到下一房间"""
        self.logger.info(f"从房间{current_room+1}移动到房间{current_room+2}")

        # 获取正确的方向
        direction = self.get_next_room_direction(current_room)
        self.logger.info(f"顺图方向: Alt+{direction}")

        # 使用Alt+方向键顺图
        if self.send_combination(['alt', direction]):
            time.sleep(2.5)  # 等待移动完成
            self.logger.info(f"✓ 成功顺图: Alt+{direction}")
            return True
        else:
            self.logger.warning(f"✗ 顺图失败: Alt+{direction}")
            return False

    def move_forward_in_room(self):
        """在房间中向前移动（让地下怪物出现）"""
        self.logger.info("第一个房间：需要靠近地下怪物位置")

        # 向前移动更多步数
        self.logger.info("向前移动...")
        for i in range(6):
            self.send_key('right')
            time.sleep(0.3)

        # 释放技能让地下怪物出现
        self.logger.info("释放技能让地下怪物出现...")
        for i in range(3):
            self.send_key(self.skill_key)
            time.sleep(0.5)

        # 继续向前走到怪物位置
        self.logger.info("继续向前走到怪物位置...")
        for i in range(3):
            self.send_key('right')
            time.sleep(0.3)

        self.logger.info("现在应该能攻击到地下怪物了")

    def clear_dungeon(self):
        """清理整个地下城"""
        self.logger.info("开始清理地下城")

        for room in range(8):  # 8个房间
            self.logger.info(f"清理房间 {room+1}/8")

            # 第一个房间需要向前移动让怪物出现
            if room == 0:
                self.move_forward_in_room()
                time.sleep(1)  # 等待怪物出现

            # 清理当前房间
            self.clear_room()

            # 移动到下一房间（除了最后一个）
            if room < 7:
                if not self.move_to_next_room(room):
                    self.logger.error(f"无法移动到房间 {room+2}")
                    # 尝试其他方向
                    self.try_alternative_directions()

        self.logger.info("地下城清理完成")
        return True

    def try_alternative_directions(self):
        """尝试其他顺图方向"""
        self.logger.info("尝试其他顺图方向")

        directions = ['down', 'up', 'left', 'right']

        for direction in directions:
            self.logger.info(f"尝试 Alt+{direction}")
            if self.send_combination(['alt', direction]):
                time.sleep(2)
                # 这里可以添加检测是否成功移动的逻辑
                self.logger.info(f"已尝试 Alt+{direction}")
                return True

        return False

    def exit_dungeon(self):
        """退出地下城"""
        self.logger.info("退出地下城")

        # 等待可能的结算
        time.sleep(3)

        # 多次按ESC退出
        for i in range(5):
            self.send_key('esc')
            time.sleep(1.5)

            # 检查是否回到城镇
            if self.find_template("town_interface"):
                self.logger.info("成功返回城镇")
                return True

        return True

    def farming_cycle(self):
        """完整的搬砖循环"""
        self.logger.info("=" * 50)
        self.logger.info("开始搬砖循环")

        try:
            # 1. 进入地下城
            if not self.enter_grandi_dungeon():
                return False

            # 2. 清理地下城
            if not self.clear_dungeon():
                return False

            # 3. 退出地下城
            if not self.exit_dungeon():
                return False

            self.logger.info("搬砖循环完成")
            return True

        except Exception as e:
            self.logger.error(f"搬砖循环异常: {e}")
            return False

def main():
    """主函数"""
    print("DNF修复版机器人")
    print("=" * 30)
    print("使用验证有效的按键方法")
    
    bot = DNFFixedBot()
    
    if not bot.find_dnf_window():
        print("错误: 未找到DNF窗口")
        return
    
    while True:
        print("\n请选择操作:")
        print("1. 测试移动")
        print("2. 测试进入地图")
        print("3. 单次搬砖循环")
        print("4. 自动搬砖循环")
        print("5. 测试模板识别")
        print("6. 查看配置和方向")
        print("7. 学习顺图方向")
        print("0. 退出")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            bot.test_movement()
        elif choice == '2':
            bot.simple_enter_map()
        elif choice == '3':
            print("\n执行单次搬砖循环...")
            print("请确保:")
            print("- 角色在城镇面向传送门")
            print("- 已手动选择格兰迪和地狱难度")
            print("- 有足够的疲劳值")
            input("确认后按回车开始...")

            if bot.farming_cycle():
                print("✓ 单次搬砖循环成功")
            else:
                print("✗ 单次搬砖循环失败")
        elif choice == '4':
            print("\n启动自动搬砖循环...")
            print("请确保:")
            print("- 角色在城镇面向传送门")
            print("- 已手动选择格兰迪和地狱难度")
            print("- 有足够的疲劳值")
            cycles = input("请输入循环次数 (默认10次): ").strip()

            try:
                max_cycles = int(cycles) if cycles else 10
            except:
                max_cycles = 10

            print(f"开始执行 {max_cycles} 次搬砖循环...")
            input("确认后按回车开始...")

            success_count = 0
            for i in range(max_cycles):
                print(f"\n第 {i+1}/{max_cycles} 轮搬砖")
                if bot.farming_cycle():
                    success_count += 1
                    print(f"✓ 第 {i+1} 轮成功")
                else:
                    print(f"✗ 第 {i+1} 轮失败")

                # 等待下一轮
                if i < max_cycles - 1:
                    print("等待下一轮...")
                    time.sleep(5)

            print(f"\n自动搬砖完成！")
            print(f"总循环: {max_cycles} 次")
            print(f"成功: {success_count} 次")
            print(f"成功率: {success_count/max_cycles*100:.1f}%")
        elif choice == '5':
            print("测试模板识别...")
            test_templates = ["town_interface", "map_selection_interface", "grandi_option"]
            for template_name in test_templates:
                result = bot.find_template(template_name)
                if result:
                    print(f"✓ {template_name}: 找到，匹配度 {result[2]:.3f}")
                else:
                    print(f"✗ {template_name}: 未找到")
        elif choice == '6':
            print(f"\n当前配置:")
            print(f"技能键: {bot.skill_key}")
            print(f"深渊模式: {'开启' if bot.enable_abyss else '关闭'}")
            print(f"已加载模板: {len(bot.templates)}个")
            print(f"已学习方向: {len(bot.grandi_directions)}个")

            if bot.grandi_directions:
                print("\n格兰迪顺图方向:")
                for i, direction in enumerate(bot.grandi_directions):
                    print(f"  房间{i+1} → 房间{i+2}: Alt+{direction}")
        elif choice == '7':
            print("\n学习顺图方向")
            print("请运行以下命令来学习正确的顺图方向:")
            print("python learn_directions.py")
            print()
            print("学习完成后，重新启动机器人即可使用学习到的方向")
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
