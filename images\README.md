# 图像模板说明

本目录用于存放游戏界面的模板图像，用于图像识别和自动化操作。

## 必需的模板图像

### 基础界面
- `town_map.png` - 城镇小地图截图
- `dungeon_map.png` - 地下城小地图截图
- `map_selection.png` - 地图选择界面截图

### 地图相关
- `grandi.png` - 格兰迪选项截图
- `hell_difficulty.png` - 地狱难度选项截图
- `abyss_button.png` - 深渊按钮截图

### 游戏流程
- `flip_card.png` - 翻牌界面截图
- `return_town.png` - 返回城镇按钮截图
- `teleport_button.png` - 传送按钮截图
- `brick_land.png` - 搬砖圣地选项截图

### 背包和装备
- `stall_machine.png` - 摆摊分解机截图
- `decompose_panel.png` - 分解面板截图
- `bag_full.png` - 背包满提示截图
- `repair_npc.png` - 修理NPC截图

## 截图要求

### 图像质量
- 使用PNG格式保存
- 保持原始分辨率
- 避免压缩失真

### 截图范围
- 只截取需要识别的关键部分
- 避免包含变化的元素（如时间、血量等）
- 确保截图内容清晰可辨

### 命名规范
- 使用英文小写字母
- 用下划线分隔单词
- 文件名要有描述性

## 获取模板图像的方法

### 1. 游戏内截图
1. 进入对应的游戏界面
2. 使用截图工具（如QQ截图、微信截图等）
3. 精确框选需要识别的区域
4. 保存为PNG格式

### 2. 使用脚本截图
```python
# 运行测试脚本
python test_modules.py
# 选择截图功能
```

### 3. 手动调整
- 如果识别效果不好，可以尝试调整截图范围
- 确保截图中没有干扰元素
- 可以截取多个相似的模板进行测试

## 注意事项

### 分辨率适配
- 不同分辨率可能需要不同的模板图像
- 建议为常用分辨率准备对应的模板

### 界面变化
- 游戏更新可能导致界面变化
- 需要及时更新对应的模板图像

### 识别精度
- 可以通过调整config.ini中的image_threshold来优化识别效果
- 建议值在0.7-0.9之间

## 模板图像示例

以下是各个模板图像应该包含的内容：

### town_map.png
- 城镇的小地图区域
- 包含地图边框和内容
- 不包含角色位置标记

### grandi.png
- 地图选择界面中的"格兰迪"文字或图标
- 确保文字清晰可读

### hell_difficulty.png
- 难度选择中的"地狱"选项
- 包含完整的选项按钮

### flip_card.png
- 翻牌界面的特征元素
- 可以是翻牌按钮或界面标题

### return_town.png
- "返回城镇"按钮的完整截图
- 确保按钮边框完整

## 故障排除

### 识别失败
1. 检查模板图像是否清晰
2. 调整识别阈值
3. 重新截取模板图像
4. 检查游戏分辨率是否匹配

### 误识别
1. 缩小模板图像范围
2. 提高识别阈值
3. 避免包含变化元素

### 性能问题
1. 适当降低图像分辨率
2. 减少不必要的模板图像
3. 优化检测频率
