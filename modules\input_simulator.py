#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
键鼠模拟模块
使用winio驱动实现键盘鼠标模拟功能
"""

import time
import ctypes
from ctypes import wintypes
import win32api
import win32con
import win32gui
from typing import Tuple, Optional
import logging

# 虚拟键码映射
VK_CODE = {
    'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46,
    'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A, 'k': 0x4B, 'l': 0x4C,
    'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
    's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58,
    'y': 0x59, 'z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
    '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
    'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74,
    'f6': 0x75, 'f7': 0x76, 'f8': 0x77, 'f9': 0x78, 'f10': 0x79,
    'f11': 0x7A, 'f12': 0x7B,
    'space': 0x20, 'enter': 0x0D, 'esc': 0x1B, 'tab': 0x09,
    'shift': 0x10, 'ctrl': 0x11, 'alt': 0x12,
    'left': 0x25, 'up': 0x26, 'right': 0x27, 'down': 0x28,
    'backspace': 0x08, 'delete': 0x2E, 'home': 0x24, 'end': 0x23,
    'pageup': 0x21, 'pagedown': 0x22, 'insert': 0x2D
}

class InputSimulator:
    """键鼠模拟器"""
    
    def __init__(self):
        self.logger = logging.getLogger("InputSimulator")
        self.game_hwnd = None
        self.init_winio()
    
    def init_winio(self):
        """初始化winio驱动"""
        try:
            # 尝试加载winio驱动
            # 注意：这里需要实际的winio.dll文件
            # 如果没有winio驱动，将使用Windows API作为备选方案
            self.use_winio = False
            self.logger.info("使用Windows API进行键鼠模拟")
        except Exception as e:
            self.logger.warning(f"winio驱动初始化失败，使用Windows API: {e}")
            self.use_winio = False
    
    def find_game_window(self, window_title="Dungeon") -> bool:
        """查找游戏窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if (window_title in window_text or
                    "Dungeon" in window_text or
                    "Fighter" in window_text or
                    "地下城与勇士" in window_text):
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            self.game_hwnd = windows[0]
            self.logger.info(f"找到游戏窗口: {win32gui.GetWindowText(self.game_hwnd)}")
            return True
        else:
            self.logger.warning("未找到游戏窗口")
            return False
    
    def activate_game_window(self) -> bool:
        """激活游戏窗口"""
        if not self.game_hwnd:
            if not self.find_game_window():
                return False
        
        try:
            win32gui.SetForegroundWindow(self.game_hwnd)
            win32gui.ShowWindow(self.game_hwnd, win32con.SW_RESTORE)
            time.sleep(0.1)
            return True
        except Exception as e:
            self.logger.error(f"激活游戏窗口失败: {e}")
            return False
    
    def press_key(self, key: str, duration: float = 0.05):
        """按键"""
        if not self.activate_game_window():
            return False
        
        vk_code = self._get_vk_code(key)
        if vk_code is None:
            self.logger.error(f"未知按键: {key}")
            return False
        
        try:
            # 按下按键
            win32api.keybd_event(vk_code, 0, 0, 0)
            time.sleep(duration)
            # 释放按键
            win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
            time.sleep(0.01)
            return True
        except Exception as e:
            self.logger.error(f"按键失败 {key}: {e}")
            return False
    
    def press_key_combination(self, keys: list, duration: float = 0.05):
        """按组合键"""
        if not self.activate_game_window():
            return False
        
        vk_codes = []
        for key in keys:
            vk_code = self._get_vk_code(key)
            if vk_code is None:
                self.logger.error(f"未知按键: {key}")
                return False
            vk_codes.append(vk_code)
        
        try:
            # 按下所有按键
            for vk_code in vk_codes:
                win32api.keybd_event(vk_code, 0, 0, 0)
            
            time.sleep(duration)
            
            # 释放所有按键（逆序）
            for vk_code in reversed(vk_codes):
                win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
            
            time.sleep(0.01)
            return True
        except Exception as e:
            self.logger.error(f"组合键失败 {keys}: {e}")
            return False
    
    def click_mouse(self, x: int, y: int, button: str = 'left', duration: float = 0.05):
        """鼠标点击"""
        if not self.activate_game_window():
            return False
        
        try:
            # 移动鼠标到指定位置
            win32api.SetCursorPos((x, y))
            time.sleep(0.01)
            
            # 点击
            if button == 'left':
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, x, y, 0, 0)
                time.sleep(duration)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, x, y, 0, 0)
            elif button == 'right':
                win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTDOWN, x, y, 0, 0)
                time.sleep(duration)
                win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTUP, x, y, 0, 0)
            
            time.sleep(0.01)
            return True
        except Exception as e:
            self.logger.error(f"鼠标点击失败 ({x}, {y}): {e}")
            return False
    
    def move_mouse(self, x: int, y: int):
        """移动鼠标"""
        try:
            win32api.SetCursorPos((x, y))
            time.sleep(0.01)
            return True
        except Exception as e:
            self.logger.error(f"鼠标移动失败 ({x}, {y}): {e}")
            return False
    
    def _get_vk_code(self, key: str) -> Optional[int]:
        """获取虚拟键码"""
        key = key.lower()
        return VK_CODE.get(key)
    
    def parse_hotkey(self, hotkey: str) -> list:
        """解析热键字符串"""
        keys = []
        parts = hotkey.lower().split('+')
        
        for part in parts:
            part = part.strip()
            if part in VK_CODE:
                keys.append(part)
            else:
                self.logger.warning(f"未知热键: {part}")
        
        return keys
